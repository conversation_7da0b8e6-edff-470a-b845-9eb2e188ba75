import { ClientCustomer } from "@/components/Customer/types";
import { del, get, post, put } from "./request";
import { OrderPayload } from "@/jotai/orders";
import axios from "axios";

// DataTree API token cache
let cachedToken: string | null = null;
let tokenExpiration: number = 0;

// Helper function to check if cached token is valid
const isTokenValid = () => cachedToken && Date.now() < tokenExpiration;

const devMode = import.meta.env.VITE_DEV_MODE || "test";
const apiBase = devMode === "prod" ? "/api/underwriting/prod" : "/api/underwriting/exp";
const adminApiBase = devMode === "prod" ? "/api/admin-server/prod" : "/api/admin-server/exp";
const bpoApiBase = devMode === "prod" ? "/api/bpo/prod" : "/api/bpo/prod";
const cmaApiBase = devMode === "prod" ? "/api/cma/prod" : "/api/cma/exp";
console.log(`Using API base: ${apiBase} (${devMode} mode)`);

// Add APIOrder type
export interface APIOrder {
  id: number;
  customerId: number;
  customerName: string;
  orderNumber: string;
  status: string;
  createdAt: string;
  updatedAt: string;
  totalPrice: number;
  properties: Array<{
    propertyId: string;
    propertyAddress: string;
    propertyStateCode: string;
    signed: boolean;
  }>;
  payload?: {
    propertyValuations?: Array<{
      propertyId: string;
      valuationData: {
        estimatedValue: number | null;
        confidenceScore: number | null;
        statusMessage: string;
      };
    }>;
    sign?: {
      [propertyId: string]: boolean;
    };
    [key: string]: any;
  };
}

// Add APIOrderResponse type to match the actual API response
interface APIOrderResponse {
  id: number;
  userId: string;
  clientCustomerId: string;
  clientName: string;
  pricingStructureName: string;
  properties: Array<{
    propertyId: string;
    propertyAddress: string;
    propertyStateCode: string;
  }>;
  orderUnitPrice: string;
  orderUnitCount: number;
  orderStatus: string;
  isActive: boolean;
  payload?: {
    isVueitNeeded: boolean;
    propertyValuations?: Array<{
      propertyId: string;
      valuationData: {
        estimatedValue: number | null;
        confidenceScore: number | null;
        statusMessage: string;
      };
    }>;
    sign?: {
      [propertyId: string]: boolean;
    };
    [key: string]: any;
  };
}

/**
 * Checks the health status of the Underwriting Test Server
 * @returns Promise resolving to the health check response
 */
export const checkServerHealth = async () => {
  try {
    // Define the health endpoint URL
    const healthEndpoint = `${apiBase}/fa/health`;

    // Use our get utility function to make the request
    const response = await get(healthEndpoint);

    console.log("Server health status:", response);
    return response;
  } catch (error) {
    console.error("Failed to check server health:", error);
    throw error;
  }
};

export const getAllCustomers = async () => {
  try {
    const endpoint = `${apiBase}/fa/customer`;
    // Rest of your function remains the same
    const response = await get(endpoint);
    console.log("All customers:", response);
    return response;
  } catch (error) {
    console.error("Failed to get all customers:", error);
    throw error;
  }
};

export const getCustomerById = async (id: number) => {
  try {
    // Define the health endpoint URL
    const endpoint = `${apiBase}/fa/customer?id=${id}`;

    // Use our get utility function to make the request
    const response = await get(endpoint);

    console.log("Customer by ID:", response);
    return response;
  } catch (error) {
    console.error("Failed to get customer by ID:", error);
    throw error;
  }
};
/**
 * Creates a new customer in the system
 * @param customerData The customer data to be created
 * @returns Promise resolving to the creation response
 */
export const createCustomer = async (customerData: ClientCustomer) => {
  try {
    // Define the endpoint URL
    const endpoint = `${apiBase}/fa/customer`;

    // Use our post utility function to make the request
    const response = await post(endpoint, customerData);

    console.log("Customer created:", response);
    return response;
  } catch (error) {
    console.error("Failed to create customer:", error);
    throw error;
  }
};

export const updateCustomer = async (customerData: ClientCustomer) => {
  try {
    // Define the endpoint URL
    const endpoint = `${apiBase}/fa/customer`;

    // Use our post utility function to make the request
    const response = await put(endpoint, customerData);

    console.log("Customer created:", response);
    return response;
  } catch (error) {
    console.error("Failed to create customer:", error);
    throw error;
  }
};

export const deleteCustomerById = async (id: number) => {
  try {
    // Define the health endpoint URL
    const endpoint = `${apiBase}/fa/customer?id=${id}`;

    // Use our get utility function to make the request
    const response = await del(endpoint);

    console.log("Customer by ID:", response);
    return response;
  } catch (error) {
    console.error("Failed to get customer by ID:", error);
    throw error;
  }
};

export const getAllPricingStructures = async () => {
  try {
    const endpoint = `${apiBase}/fa/pricing-structure`;
    // Rest of your function remains the same
    const response = await get(endpoint);
    console.log("All pricing structures:", response);
    return response;
  } catch (error) {
    console.error("Failed to get all pricing structures:", error);
    throw error;
  }
};

export const postCreateOrder = async (order: OrderPayload) => {
  try {
    // Define the endpoint URL
    const endpoint = `${apiBase}/fa/order`;

    // Use our post utility function to make the request
    const response = await post(endpoint, order);

    console.log("Customer created:", response);
    return response;
  } catch (error) {
    console.error("Failed to create customer:", error);
    throw error;
  }
};

export const updateUserPassword = async (username: string, password: string) => {
  try {
    const endpoint = `${adminApiBase}/api/cognito/users/update-password-permanent`;
    // const endpoint = `http://localhost:9027/api/cognito/users/update-password-permanent`;
    const response = await put(endpoint, {
      userPoolId: "us-east-1_2FeMjm1CH",
      username: username,
      newPassword: password,
    });
    return response;
  } catch (error) {
    console.error("Failed to update user password:", error);
    throw error;
  }
};

interface PricingStructurePayload {
  id: number;
  standardPricing: number;
}

export const postUpdatePricingStructure = async (pricingStructure: PricingStructurePayload) => {
  try {
    // Define the endpoint URL
    const endpoint = `${apiBase}/fa/pricing-structure?id=${pricingStructure.id}&standard-pricing=${pricingStructure.standardPricing}`;

    // Use our post utility function to make the request
    const response = await post(endpoint);

    console.log("Customer created:", response);
    return response;
  } catch (error) {
    console.error("Failed to create customer:", error);
    throw error;
  }
};

interface PropertyDetails {
  PropertyId: number;
  Address: string;
  City: string;
  State: string;
  Zip: number;
  County: string;
  StreetName: string;
  StreetDir?: string;
  StreetType: string;
  StreetNumber: string;
}

interface ValuationSummary {
  EstimatedValue: number | null;
  ConfidenceScore: number | null;
  StandardDeviation: number | null;
  AVMStatusCode: string;
  AVMStatusMessage: string;
}

interface ValidateAddressResponse {
  isValid: boolean;
  propertyDetails?: PropertyDetails;  // The first item from LitePropertyList if exists
  valuationSummary?: ValuationSummary;
}

export const postValidateAddress = async (address: string): Promise<ValidateAddressResponse> => {
  try {
    // Check if we need to get a new token
    if (!isTokenValid()) {
      console.log("Retrieving new DataTree API token...");
      
      const authPayload = {
        ClientId: "b6c463bb-ccda-49fd-9837-315ba30ec249",
        ClientSecretKey: "****************************************",
      };
      
      console.log("Auth request payload:", authPayload);
      
      try {
        const authResponse = await axios.post(
          "https://dtapiuat.datatree.com/api/Login/AuthenticateClient?Ver=1.0",
          authPayload,
          {
            headers: {
              "Content-Type": "application/json"
            }
          }
        );
        
        console.log("Auth API raw response type:", typeof authResponse.data);
        
        // The token is returned directly as a string
        if (typeof authResponse.data !== 'string') {
          console.error("Auth response invalid format:", authResponse.data);
          throw new Error("Failed to obtain authentication token - invalid response format");
        }

        cachedToken = authResponse.data;
        tokenExpiration = Date.now() + 3600000; // 1 hour expiration
        console.log("Address validation token retrieved and cached");
      } catch (err: any) {
        console.error("Auth API error details:", {
          error: err,
          message: err.message,
          response: err.response?.data,
          status: err.response?.status
        });
        throw err;
      }
    }

    // Define the endpoint URL for property search
    const endpoint = "https://dtapiuat.datatree.com/api/Search/PropertySearch?Ver=1.0";

    // Prepare request body for property search
    const searchPayload = {
      CountOnly: false,
      MaxReturn: 1,
      ProductName: "SearchStandard",
      SpatialType: "Geography",
      Filters: [
        {
          FilterName: "FullAddress",
          FilterOperator: "is",
          FilterValues: [address],
          FilterGroup: 0
        }
      ]
    };

    // Make property search request with cached token
    const response = await axios.post(endpoint, searchPayload, {
      headers: {
        Authorization: `Bearer ${cachedToken}`,
        "Content-Type": "application/json"
      }
    });

    // Parse the response
    const propertyData = response.data?.LitePropertyList?.[0];
    const isValid = propertyData?.PropertyId != null;
    console.log("Address validation completed:", isValid ? "Valid" : "Invalid");
    
    // If address is valid, fetch valuation data
    let valuationSummary: ValuationSummary | undefined;
    if (isValid && propertyData.PropertyId) {
      try {
        const valuationEndpoint = "https://dtapiuat.datatree.com/api/Report/GetReport?Ver=1.0";
        const valuationPayload = {
          ProductNames: ["ProcisionPremier"],
          SearchType: "PROPERTY",
          PropertyID: propertyData.PropertyId.toString()
        };

        const valuationResponse = await axios.post(valuationEndpoint, valuationPayload, {
          headers: {
            Authorization: `Bearer ${cachedToken}`,
            "Content-Type": "application/json"
          }
        });

        // Extract ValuationSummary from the response
        valuationSummary = valuationResponse.data?.Reports?.[0]?.Data?.ValuationSummary;
        console.log("Valuation data fetched successfully");
      } catch (valuationError) {
        console.error("Failed to fetch valuation data:", valuationError);
        // Continue without valuation data - don't fail the whole request
      }
    }
    
    return {
      isValid,
      propertyDetails: isValid ? propertyData : undefined,
      valuationSummary
    };
  } catch (error) {
    console.error("Failed to validate address:", error);
    // Clear token cache if authentication error occurs
    if (error instanceof Error && 
        (error.message?.includes("authentication") || 
         (error as any).response?.status === 401)) {
      cachedToken = null;
      tokenExpiration = 0;
    }
    return { isValid: false };
  }
};

export const getAllOrders = async (): Promise<APIOrder[]> => {
  try {
    const endpoint = `${apiBase}/fa/orders`;
    const response = await get(endpoint) as APIOrderResponse[];
    console.log("Raw API response from getAllOrders:", response);
    
    // Transform the response to match our APIOrder type
    return response.map(order => ({
      id: order.id,
      customerId: parseInt(order.clientCustomerId),
      customerName: order.clientName,
      orderNumber: `${order.id}`,
      status: order.orderStatus,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      totalPrice: parseFloat(order.orderUnitPrice) * order.orderUnitCount,
      properties: order.properties.map(prop => ({
        ...prop,
        // Check if this property is signed based on payload.sign field (flat structure)
        signed: order.payload?.sign?.[prop.propertyId] === true || false
      })),
      payload: order.payload
    }));
  } catch (error) {
    console.error("Failed to get all orders:", error);
    throw error;
  }
};

export const updateOrder = async (orderId: number, order: Partial<OrderPayload>) => {
  try {
    const endpoint = `${apiBase}/fa/order?id=${orderId}`;  // Changed from orderId to id
    console.log("Updating order with payload structure:", JSON.stringify(order, null, 2));  // Add logging
    const response = await put(endpoint, order);
    console.log("Order update response:", response);
    return response;
  } catch (error) {
    console.error("Failed to update order:", error);
    throw error;
  }
};

export const updatePropertySignedStatus = async (orderId: number, propertyId: string, signed: boolean) => {
  try {
    const endpoint = `${apiBase}/fa/order/property/sign`;
    const response = await put(endpoint, {
      orderId,
      propertyId,
      signed
    });
    console.log("Property signed status updated:", response);
    return response;
  } catch (error) {
    console.error("Failed to update property signed status:", error);
    throw error;
  }
};

export const getBPOPDFInfo = async (params: {propertyId: string}) => {
  try {
    const endpoint = `${bpoApiBase}/bpo/info?property_id=${params.propertyId}`;
    // Rest of your function remains the same
    const response = await get(endpoint);
    console.log("BPO PDF info:", response);
    return response;
  } catch (error) {
    console.error("Failed to get BPO PDF info:", error);
    throw error;
  }
};
export const getGeocodingInfo= async (params: {address: string}) => {
  try {
    const endpoint = `${cmaApiBase}/geocoding?address=${params.address}`;
    // Rest of your function remains the same
    const response = await get(endpoint);
    console.log("Geocoding info:", response);
    return response;
  } catch (error) {
    console.error("Failed to get Geocoding info:", error);
    throw error;
  }
};