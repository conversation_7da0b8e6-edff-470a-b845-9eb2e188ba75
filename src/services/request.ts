import { fetchAuthSession } from "aws-amplify/auth";
import axios, { AxiosRequestConfig, AxiosResponse } from "axios";
const needsAuth = true;
export const getUserToken = async (type: "id" | "access") => {
  if (type !== "id" && type !== "access") throw new Error("Invalid token type");

  const { idToken, accessToken } = (await fetchAuthSession()).tokens || {};
  if (idToken && type === "id") {
    return idToken.toString() || "";
  } else if (accessToken && type === "access") {
    return accessToken.toString() || "";
  }
  return null;
};

/**
 * Makes an authenticated HTTP request
 * @param url The URL to make the request to
 * @param parameter Request parameters including method, data, headers, etc.
 * @param options Additional options for the request handling
 * @returns Promise resolving to the response data or full response
 */
export const request = async <T = any>(
  url: string,
  parameter: AxiosRequestConfig,
  options: {
    processData?: boolean;
  } = {}
): Promise<T | AxiosResponse<T>> => {
  const { processData = true } = options;

  try {
    // Get the user access token
    let accessToken = await getUserToken("access");

    // Create authorization header
    const Authorization = `Bearer ${accessToken}`;

    // // Make the request with authorization header
    // const response: AxiosResponse<T> = await axios(url, {
    //   ...parameter,
    //   headers: {
    //     Authorization,
    //     'Content-Type': 'application/json',
    //     ...parameter.headers
    //   },
    // });
    const response: AxiosResponse<T> = await axios(url, {
      ...parameter,
      headers: {
        // Only include Authorization if truly needed
        ...(needsAuth ? { Authorization } : {}),
        "Content-Type": "application/json",
        ...parameter.headers,
      },
    });

    // Return the processed response data if processData is true, otherwise return the full response
    return processData ? response.data : response;
  } catch (error: any) {
    // Handle network errors
    if (error.request && !error.response) {
      console.error("Network error:", error);
      throw new Error("Network error. Please check your connection.");
    }

    // Handle API errors
    if (error.response) {
      const { status, data } = error.response;

      // Specific error handling based on status codes
      switch (status) {
        case 400:
          console.error("Bad request:", data);
          throw new Error(data.message || "Invalid request");
        case 401:
          console.error("Unauthorized:", data);
          throw new Error("Authentication failed. Please log in again.");
        case 403:
          console.error("Forbidden:", data);
          throw new Error("You do not have permission to access this resource");
        case 404:
          console.error("Not found:", data);
          throw new Error("The requested resource was not found");
        case 500:
          console.error("Server error:", data);
          throw new Error("Server error. Please try again later.");
        default:
          console.error(`Error ${status}:`, data);
          throw new Error(data.message || "An error occurred");
      }
    }

    // Handle unexpected errors
    console.error("Request failed:", error);
    throw error;
  }
};

/**
 * Convenience method for making GET requests
 */
export const get = <T = any>(url: string, params = {}, options = {}): Promise<T> => {
  return request<T>(url, { method: "GET", params }, options) as Promise<T>;
};

/**
 * Convenience method for making POST requests
 */
export const post = <T = any>(url: string, data = {}, options = {}): Promise<T> => {
  return request<T>(url, { method: "POST", data }, options) as Promise<T>;
};

/**
 * Convenience method for making PUT requests
 */
export const put = <T = any>(url: string, data = {}, options = {}): Promise<T> => {
  return request<T>(url, { method: "PUT", data }, options) as Promise<T>;
};

/**
 * Convenience method for making DELETE requests
 */
export const del = <T = any>(url: string, params = {}, options = {}): Promise<T> => {
  return request<T>(url, { method: "DELETE", params }, options) as Promise<T>;
};

/**
 * Convenience method for making PATCH requests
 */
export const patch = <T = any>(url: string, data = {}, options = {}): Promise<T> => {
  return request<T>(url, { method: "PATCH", data }, options) as Promise<T>;
};
