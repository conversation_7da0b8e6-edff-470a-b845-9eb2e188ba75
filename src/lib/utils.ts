import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export type ConfidenceLevel = 'high' | 'medium' | 'low' | 'na';

export const getConfidenceLevel = (score: number | null): ConfidenceLevel => {
  if (score === null) return 'na';
  if (score >= 75) return 'high';
  if (score >= 45) return 'medium';
  return 'low';
};

export const getConfidenceLevelColor = (level: ConfidenceLevel, isDarkMode: boolean = false): string => {
  switch (level) {
    case 'high':
      return isDarkMode ? 'text-green-400' : 'text-green-600';
    case 'medium':
      return isDarkMode ? 'text-yellow-400' : 'text-yellow-600';
    case 'low':
      return isDarkMode ? 'text-red-400' : 'text-red-600';
    default:
      return isDarkMode ? 'text-gray-400' : 'text-gray-600';
  }
};

export const getConfidenceLevelBg = (level: ConfidenceLevel, isDarkMode: boolean = false): string => {
  switch (level) {
    case 'high':
      return isDarkMode ? 'bg-green-950/30' : 'bg-green-50';
    case 'medium':
      return isDarkMode ? 'bg-yellow-950/30' : 'bg-yellow-50';
    case 'low':
      return isDarkMode ? 'bg-red-950/30' : 'bg-red-50';
    default:
      return isDarkMode ? 'bg-gray-950/30' : 'bg-gray-50';
  }
};

export const getConfidenceLevelDescription = (level: ConfidenceLevel): string => {
  switch (level) {
    case 'high':
      return 'High confidence (75-100%)';
    case 'medium':
      return 'Medium confidence (45-74%)';
    case 'low':
      return 'Low confidence (0-44%)';
    default:
      return 'Confidence score not available';
  }
};
