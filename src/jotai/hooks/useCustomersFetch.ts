import { useAtom } from "jotai";
import { customers<PERSON>tom, errorAtom, isLoading<PERSON>tom, useCustomersQuery } from "../customers";
import { useEffect } from "react";

export const useCustomersFetch = () => {
  // Destructure refetch from the useCustomersQuery result
  const { data, isLoading, error, refetch } = useCustomersQuery();
  const [, setCustomers] = useAtom(customersAtom);
  const [, setIsLoading] = useAtom(isLoadingAtom);
  const [, setError] = useAtom(errorAtom);

  useEffect(() => {
    setIsLoading(isLoading);
  }, [isLoading, setIsLoading]);

  useEffect(() => {
    if (data) {
      setCustomers(data);
    }
  }, [data, setCustomers]);

  useEffect(() => {
    if (error) {
      setError(error instanceof Error ? error.message : "Unknown error occurred");
    } else {
      setError(null);
    }
  }, [error, setError]);

  // Return the query result including refetch
  return { data, isLoading, error, refetch };
};

