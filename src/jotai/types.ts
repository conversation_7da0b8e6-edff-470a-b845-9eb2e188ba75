export interface AuthUser {
    username?:     string;
    userId:        string;
    userGroup?:    string[] | string;
    userEmail:     string;
    signInDetails?: SignInDetails;
}

export interface SignInDetails {
    loginId:      string;
    authFlowType: string;
}

export type OrderStatus = 'draft' | 'submitted' | 'in progress' | 'in review' | 'done';

export interface Order {
  id: number;
  customerId: number;
  customerName: string;
  pricingStructure: {
    id: number;
    name: string;
  };
  price: number;
  status: OrderStatus;
  lastUpdated: string;
  properties: Array<{
    propertyAddress: string;
    propertyStateCode: string;
    price: number;
  }>;
}

export interface Property {
  propertyId: string;
  propertyAddress: string;
  propertyStateCode: string;
  signed: boolean;
}
