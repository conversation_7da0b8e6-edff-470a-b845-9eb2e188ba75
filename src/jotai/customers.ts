import { ClientCustomer } from "@/components/Customer/types";
import { createCustomer, getAllCustomers, updateCustomer, deleteCustomerById } from "@/services/data";
import { useQuery } from "@tanstack/react-query";
import { atom } from "jotai";

export const customersAtom = atom<ClientCustomer[]>([]);
export const searchTermAtom = atom<string>("");
export const searchResultsAtom = atom<ClientCustomer[]>([]);
export const selectedCustomerAtom = atom<ClientCustomer | null>(null);
export const isLoadingAtom = atom<boolean>(false);
export const errorAtom = atom<string | null>(null);

const devMode = import.meta.env.VITE_DEV_MODE || "test";
export const useCustomersQuery = () => {
  return useQuery({
    queryKey: ['customers'],
    queryFn: getAllCustomers
  });
};
export const filteredCustomersAtom = atom((get) => {
  const searchTerm = get(searchTermAtom);
  const customers = get(customersAtom);

  if (!searchTerm.trim()) {
    return customers;
  }

  // Search by ID, account number, company name, or email
  return customers.filter(
    (customer) =>
      customer.id?.toString().includes(searchTerm) ||
      customer.sourceAcctNo.toString().includes(searchTerm) ||
      customer.company.toLowerCase().includes(searchTerm.toLowerCase()) ||
      customer.email.toLowerCase().includes(searchTerm.toLowerCase())
  );
});
export const fetchCustomersAtom = atom(null, async (_, set) => {
  try {
    set(isLoadingAtom, true);
    set(errorAtom, null);

    const customers = await getAllCustomers();

    set(customersAtom, customers);
  } catch (error) {
    set(errorAtom, error instanceof Error ? error.message : "Unknown error occurred");
  } finally {
    set(isLoadingAtom, false);
  }
});

export const searchCustomersAtom = atom(null, (get, set) => {
  const filteredCustomers = get(filteredCustomersAtom);
  set(searchResultsAtom, filteredCustomers);

  if (filteredCustomers.length === 1) {
    set(selectedCustomerAtom, filteredCustomers[0]);
  } else if (filteredCustomers.length === 0) {
    set(selectedCustomerAtom, null);
  }
});

// Modal states
export const isCreateModalOpenAtom = atom<boolean>(false);
export const isCreatingCustomerAtom = atom<boolean>(false);
export const createCustomerErrorAtom = atom<string | null>(null);

export const newCustomerFormAtom = atom<Partial<ClientCustomer>>({
  isActiveFlag: true,
  isTaxableFlag: true,
  creditCardFlag: false,
  isBillable: true,
  isTestData: false,
  pricingStructures: []
});

export const createCustomerAtom = atom(
  null,
  async (get, set) => {
    const newCustomer = get(newCustomerFormAtom);
    
    try {
      set(isCreatingCustomerAtom, true);
      set(createCustomerErrorAtom, null);
      
      const randomAcctNo = Math.floor(10000 + Math.random() * 90000);
      
      // Add isTestData flag based on devMode and include sourceAcctNo
      const customerData = {
        ...newCustomer,
        sourceAcctNo: randomAcctNo,
        isTestData: devMode === "test" ? true : false,
        pricingStructures: newCustomer.pricingStructures || []
      };

      await createCustomer(customerData as ClientCustomer);
      
      const customers = await getAllCustomers();
      set(customersAtom, customers);
      
      set(isCreateModalOpenAtom, false);
      
      // Reset
      set(newCustomerFormAtom, {
        isActiveFlag: true,
        isTaxableFlag: true,
        creditCardFlag: false,
        isBillable: true,
        isTestData: devMode === "test" ? true : false,
        pricingStructures: []
      });
      
    } catch (error) {
      set(createCustomerErrorAtom, error instanceof Error ? error.message : 'Failed to create customer');
    } finally {
      set(isCreatingCustomerAtom, false);
    }
  }
);

// Edit modal states
export const isEditModalOpenAtom = atom<boolean>(false);
export const isUpdatingCustomerAtom = atom<boolean>(false);
export const updateCustomerErrorAtom = atom<string | null>(null);
export const editCustomerFormAtom = atom<ClientCustomer | null>(null);

export const initEditFormAtom = atom(null, (get, set) => {
  const selectedCustomer = get(selectedCustomerAtom);
  if (selectedCustomer) {
    set(editCustomerFormAtom, { 
      ...selectedCustomer,
      pricingStructures: selectedCustomer.pricingStructures || []
    });
    set(isEditModalOpenAtom, true);
  }
});

// Update customer atom
export const updateCustomerAtom = atom(
  null,
  async (get, set) => {
    const customerToUpdate = get(editCustomerFormAtom);
    
    if (!customerToUpdate || !customerToUpdate.id) {
      set(updateCustomerErrorAtom, 'No customer selected for update');
      return;
    }
    
    try {
      set(isUpdatingCustomerAtom, true);
      set(updateCustomerErrorAtom, null);
      
      const updateData = {
        ...customerToUpdate,
        pricingStructures: customerToUpdate.pricingStructures || []
      };
      
      await updateCustomer(updateData);
      
      const customers = await getAllCustomers();
      set(customersAtom, customers);
      
      const updatedCustomer = customers.find((c:ClientCustomer) => c.id === customerToUpdate.id) || null;
      set(selectedCustomerAtom, updatedCustomer);
      
      set(isEditModalOpenAtom, false);
      set(editCustomerFormAtom, null);
      
    } catch (error) {
      set(updateCustomerErrorAtom, error instanceof Error ? error.message : 'Failed to update customer');
    } finally {
      set(isUpdatingCustomerAtom, false);
    }
  }
);

// Delete confirmation modal state
export const isDeleteConfirmOpenAtom = atom<boolean>(false);
export const isDeletingCustomerAtom = atom<boolean>(false);
export const deleteCustomerErrorAtom = atom<string | null>(null);

// Delete customer atom
export const deleteCustomerAtom = atom(
  null,
  async (get, set) => {
    const selectedCustomer = get(selectedCustomerAtom);
    
    if (!selectedCustomer || !selectedCustomer.id) {
      set(deleteCustomerErrorAtom, 'No customer selected for deletion');
      return;
    }
    
    try {
      set(isDeletingCustomerAtom, true);
      set(deleteCustomerErrorAtom, null);
      
      await deleteCustomerById(selectedCustomer.id);

      const customers = await getAllCustomers();
      set(customersAtom, customers);
      
      set(selectedCustomerAtom, null);
      
      set(isDeleteConfirmOpenAtom, false);
      
    } catch (error) {
      set(deleteCustomerErrorAtom, error instanceof Error ? error.message : 'Failed to delete customer');
    } finally {
      set(isDeletingCustomerAtom, false);
    }
  }
);