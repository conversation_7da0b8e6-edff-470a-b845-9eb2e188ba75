import { atom } from "jotai";
import { ClientCustomer } from "@/components/Customer/types";

// Order flow states
export const orderStepAtom = atom<number>(1); // Current step in order process
export const selectedOrderCustomerAtom = atom<ClientCustomer | null>(null);

// Customer search functionality
export const customerSearchTermAtom = atom<string>("");
export const customerSearchResultsAtom = atom<ClientCustomer[]>([]);
export const isSearchingCustomersAtom = atom<boolean>(false);
export const customerSearchErrorAtom = atom<string | null>(null);

// Order configuration
export interface ValidatedAddress {
  fullAddress: string;
  propertyId?: string;
  valuationData?: {
    estimatedValue: number | null;
    confidenceScore: number | null;
    statusMessage: string;
  };
}

export const selectedPricingStructureAtom = atom<any>(null);
export const propertyAddressesAtom = atom<ValidatedAddress[]>([]);
export const currentAddressInputAtom = atom<string>("");
export const isAddressValidAtom = atom<boolean | null>(null);
export const isValidatingAddressAtom = atom<boolean>(false);
export const addressSuggestionsAtom = atom<string[]>([]);
export const customerOrderNumberAtom = atom<string>("");
export const orderEmailAtom = atom<string>("");
export const orderPhoneAtom = atom<string>("");
export const isBatchUploadModalOpenAtom = atom<boolean>(false);
export const isVueitNeededAtom = atom<boolean>(false);

// Order data
export interface OrderData {
  customer: ClientCustomer;
  pricingStructure: any;
  propertyAddresses: ValidatedAddress[];
  customerOrderNumber?: string;
  email: string;
  phone: string;
  isVueitNeeded: boolean;
}

export const orderDataAtom = atom<Partial<OrderData>>({});

// Order payload interface for API submission
export interface OrderPayload {
  clientCustomerId?: string;
  pricingStructureId?: number;
  properties?: Array<{
    propertyId: string;
    propertyAddress: string;
    propertyStateCode: string;
    signed?: boolean;
  }>;
  isActive?: boolean;
  isTestData?: boolean;
  payload?: {
    isVueitNeeded?: boolean;
    propertyValuations?: Array<{
      propertyId: string;
      valuationData: {
        estimatedValue: number | null;
        confidenceScore: number | null;
        statusMessage: string;
      };
    }>;
    signedProperties?: {
      [propertyId: string]: {
        signedAt: string;
        signedBy: string;
      };
    };
    sign?: {
      [propertyId: string]: boolean;
    };
    [key: string]: any;
  };
}

// Derived atom for filtered customer search results
export const filteredOrderCustomersAtom = atom((get) => {
  const searchTerm = get(customerSearchTermAtom);
  const searchResults = get(customerSearchResultsAtom);

  if (!searchTerm.trim()) {
    return [];
  }

  return searchResults.filter(
    (customer) =>
      customer.company?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (customer.id && customer.id.toString().includes(searchTerm)) ||
      (customer.sourceAcctNo && customer.sourceAcctNo.toString().includes(searchTerm)) ||
      customer.email?.toLowerCase().includes(searchTerm.toLowerCase())
  );
});

// Reset order state
export const resetOrderAtom = atom(null, (_, set) => {
  set(orderStepAtom, 1);
  set(selectedOrderCustomerAtom, null);
  set(customerSearchTermAtom, "");
  set(customerSearchResultsAtom, []);
  set(customerSearchErrorAtom, null);
  set(selectedPricingStructureAtom, null);
  set(propertyAddressesAtom, [] as ValidatedAddress[]);
  set(currentAddressInputAtom, "");
  set(isAddressValidAtom, null);
  set(addressSuggestionsAtom, []);
  set(customerOrderNumberAtom, "");
  set(orderEmailAtom, "");
  set(orderPhoneAtom, "");
  set(isBatchUploadModalOpenAtom, false);
  set(isVueitNeededAtom, false);
  set(orderDataAtom, {});
});

export interface AddressDiscrepancy {
  originalAddress: string;
  suggestedAddress: string;
  propertyId?: string;
}

export const addressDiscrepancyAtom = atom<AddressDiscrepancy | null>(null);
export const isAddressDiscrepancyModalOpenAtom = atom<boolean>(false);