import { AuthUser } from '@/jotai/types';

/**
 * Checks if the user has admin privileges by looking for 'bpo-admin' in their userGroup
 * @param user - The authenticated user object
 * @returns boolean indicating if the user is an admin
 */
export const isUserAdmin = (user: AuthUser | null): boolean => {
  if (!user?.userGroup) return false;
  
  // Handle both string array and string cases
  if (Array.isArray(user.userGroup)) {
    return user.userGroup.includes('bpo-admin');
  }
  
  return user.userGroup === 'bpo-admin';
}; 