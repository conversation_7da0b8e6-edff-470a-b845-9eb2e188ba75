// App.tsx
import { useAtom } from "jotai";
import { userAtom } from "./jotai/user";
import CustomerSearch from "./components/Customer";
import { Routes, Route } from "react-router-dom";
import OrderPage from "./components/Order/OrderPage";
import ProductPricing from "./components/ProductPricing";
function App() {
  const [user] = useAtom(userAtom);
  console.log("App user", user);
  return (
    <div>
      <Routes>
        <Route path="/" element={<CustomerSearch />} />
        <Route path="/customer" element={<CustomerSearch />} />
        <Route path="/order" element={<OrderPage />} /> 
        <Route path="/product-pricing" element={<ProductPricing />} />
      </Routes>
    </div>
  );
}

export default App;
