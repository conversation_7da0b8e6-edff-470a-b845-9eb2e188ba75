import { useAtom } from "jotai";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { FileText } from "lucide-react";

import { customerOrderNumberAtom } from "@/jotai/orders";

const CustomerOrderNumberForm = () => {
  const [customerOrderNumber, setCustomerOrderNumber] = useAtom(customerOrderNumberAtom);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <FileText className="h-5 w-5" />
          <span>Customer Order Number (Optional)</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <Label htmlFor="customer-order-number">Customer Order Number</Label>
          <Input
            id="customer-order-number"
            placeholder="Enter customer's order number if provided..."
            value={customerOrderNumber}
            onChange={(e) => setCustomerOrderNumber(e.target.value)}
            className="max-w-md"
          />
          <p className="text-sm text-muted-foreground">
            This is the order number provided by the customer for their internal tracking
          </p>
        </div>
      </CardContent>
    </Card>
  );
};

export default CustomerOrderNumberForm;