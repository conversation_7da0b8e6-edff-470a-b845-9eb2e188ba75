import { useAtom } from "jotai";
import {
  propertyAddressesAtom,
  currentAddressInputAtom,
  isAddressValidAtom,
  isValidatingAddressAtom,
  addressSuggestionsAtom,
  addressDiscrepancyAtom,
  isAddressDiscrepancyModalOpenAtom,
  ValidatedAddress
} from "@/jotai/orders";
import { postValidateAddress } from "@/services/data";

export const useAddressValidation = () => {
  const [propertyAddresses, setPropertyAddresses] = useAtom(propertyAddressesAtom);
  const [currentAddressInput, setCurrentAddressInput] = useAtom(currentAddressInputAtom);
  const [isAddressValid, setIsAddressValid] = useAtom(isAddressValidAtom);
  const [, setIsValidatingAddress] = useAtom(isValidatingAddressAtom);
  const [, setAddressSuggestions] = useAtom(addressSuggestionsAtom);
  const [discrepancy, setAddressDiscrepancy] = useAtom(addressDiscrepancyAtom);
  const [, setIsDiscrepancyModalOpen] = useAtom(isAddressDiscrepancyModalOpenAtom);

  // Real address validation using DataTree API
  const validateAddress = async (address: string) => {
    if (!address.trim()) {
      setIsAddressValid(null);
      setAddressSuggestions([]);
      return;
    }

    setIsValidatingAddress(true);
    
    try {
      const result = await postValidateAddress(address);
      
      if (result.isValid && result.propertyDetails) {
        // Construct API returned address
        const apiAddress = `${result.propertyDetails.Address}, ${result.propertyDetails.City}, ${result.propertyDetails.State} ${result.propertyDetails.Zip}`;
        
        // Compare addresses (ignoring case and basic formatting)
        const normalizedInput = address.toLowerCase().replace(/\s+/g, ' ').trim();
        const normalizedApi = apiAddress.toLowerCase().replace(/\s+/g, ' ').trim();
        
        if (normalizedInput !== normalizedApi) {
          // Address discrepancy found
          setIsAddressValid(null); // Keep validation pending until user decides
          setAddressDiscrepancy({
            originalAddress: address,
            suggestedAddress: apiAddress,
            propertyId: result.propertyDetails.PropertyId.toString()
          });
          setIsDiscrepancyModalOpen(true);
        } else {
          // Addresses match
          setIsAddressValid(true);
          setAddressDiscrepancy(null);
        }
      } else {
        setIsAddressValid(false);
        setAddressDiscrepancy(null);
      }
      
      setAddressSuggestions([]); // Clear any previous suggestions
    } catch (error) {
      console.error("Address validation failed:", error);
      setIsAddressValid(false);
      setAddressDiscrepancy(null);
      setAddressSuggestions([]);
    } finally {
      setIsValidatingAddress(false);
    }
  };

  const handleAddressChange = (value: string) => {
    setCurrentAddressInput(value);
    setIsAddressValid(null);
    setAddressDiscrepancy(null);
    
    // Debounced validation
    const timeoutId = setTimeout(() => {
      validateAddress(value);
    }, 500);
    
    return () => clearTimeout(timeoutId);
  };

  const handleAddressSuggestionSelect = (suggestion: string) => {
    setCurrentAddressInput(suggestion);
    setIsAddressValid(true);
    setAddressSuggestions([]);
    setAddressDiscrepancy(null);
  };

  const handleDiscrepancyResolution = (useSuggested: boolean) => {
    if (!discrepancy) return;
    
    if (useSuggested && discrepancy.propertyId) {
      // Re-validate to get the valuation data
      postValidateAddress(discrepancy.suggestedAddress).then(result => {
        if (result.isValid && result.propertyDetails) {
          // Use the suggested address
          const validatedAddress: ValidatedAddress = {
            fullAddress: discrepancy.suggestedAddress,
            propertyId: discrepancy.propertyId,
            valuationData: result.valuationSummary ? {
              estimatedValue: result.valuationSummary.EstimatedValue,
              confidenceScore: result.valuationSummary.ConfidenceScore,
              statusMessage: result.valuationSummary.AVMStatusMessage
            } : undefined
          };
          setPropertyAddresses(prev => [...prev, validatedAddress]);
        }
      });
    }
    
    // Clear all states
    setAddressDiscrepancy(null);
    setIsDiscrepancyModalOpen(false);
    setIsAddressValid(null);
    setCurrentAddressInput("");
    setAddressSuggestions([]);
  };

  const addAddress = async () => {
    if (currentAddressInput.trim() && isAddressValid === true) {
      try {
        const result = await postValidateAddress(currentAddressInput.trim());
        if (result.isValid && result.propertyDetails) {
          const validatedAddress: ValidatedAddress = {
            fullAddress: currentAddressInput.trim(),
            propertyId: result.propertyDetails.PropertyId.toString(),
            valuationData: result.valuationSummary ? {
              estimatedValue: result.valuationSummary.EstimatedValue,
              confidenceScore: result.valuationSummary.ConfidenceScore,
              statusMessage: result.valuationSummary.AVMStatusMessage
            } : undefined
          };
          setPropertyAddresses([...propertyAddresses, validatedAddress]);
          setCurrentAddressInput("");
          setIsAddressValid(null);
          setAddressSuggestions([]);
        }
      } catch (error) {
        console.error("Failed to validate address for adding:", error);
      }
    }
  };

  const removeAddress = (index: number) => {
    const newAddresses = propertyAddresses.filter((_, i) => i !== index);
    setPropertyAddresses(newAddresses);
  };

  return {
    validateAddress,
    handleAddressChange,
    handleAddressSuggestionSelect,
    handleDiscrepancyResolution,
    addAddress,
    removeAddress,
  };
};