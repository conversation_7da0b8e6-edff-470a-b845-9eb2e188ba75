import { use<PERSON><PERSON> } from "jotai";
import { useState, useEffect } from "react";
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { CheckCircle, Package, DollarSign } from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

import {
  selectedOrderCustomerAtom,
  selectedPricingStructureAtom,
} from "@/jotai/orders";
import { getAllPricingStructures } from "@/services/data";
import { PricingStructure } from "@/components/Customer/types";

const PricingStructureSelection = () => {
  const [selectedCustomer] = useAtom(selectedOrderCustomerAtom);
  const [selectedPricingStructure, setSelectedPricingStructure] = useAtom(selectedPricingStructureAtom);
  const [availablePricingStructures, setAvailablePricingStructures] = useState<Array<PricingStructure & {
    isSpecialPricing?: boolean;
    specialPricing?: number | null;
    customerPricingId?: number;
  }>>([]);

  // Format price consistently
  const formatPrice = (price: string | number | null) => {
    if (price === null || price === undefined) return "N/A";
    const numPrice = typeof price === "string" ? parseFloat(price) : price;
    return `$${numPrice.toFixed(2)}`;
  };

  // Load customer's pricing structures
  useEffect(() => {
    const loadPricingStructures = async () => {
      try {
        const allStructures = await getAllPricingStructures();
        
        if (!selectedCustomer?.pricingStructures || selectedCustomer.pricingStructures.length === 0) {
          // If customer has no pricing structures, load all available ones
          setAvailablePricingStructures(allStructures);
          return;
        }

        // Create a map to store the most recent pricing structure for each ID
        const structureMap = new Map();
        
        // Process customer's pricing structures, keeping only the most recent one for each ID
        selectedCustomer.pricingStructures.forEach(customerPS => {
          const fullStructure = allStructures.find((ps: PricingStructure) => ps.id === customerPS.pricingStructureId);
          if (fullStructure) {
            const existingStructure = structureMap.get(customerPS.pricingStructureId);
            if (!existingStructure || (customerPS.id && existingStructure.customerPricingId && customerPS.id > existingStructure.customerPricingId)) {
              structureMap.set(customerPS.pricingStructureId, {
                ...fullStructure,
                isSpecialPricing: customerPS.specialPricing !== null && customerPS.specialPricing !== undefined,
                specialPricing: customerPS.specialPricing ?? null,
                customerPricingId: customerPS.id
              });
            }
          }
        });
        
        setAvailablePricingStructures(Array.from(structureMap.values()));
      } catch (error) {
        console.error("Failed to load pricing structures:", error);
      }
    };

    loadPricingStructures();
  }, [selectedCustomer]);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Package className="h-5 w-5" />
          <span>Select Pricing Structure</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {availablePricingStructures.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <Package className="h-12 w-12 mx-auto mb-3 opacity-50" />
            <p>No pricing structures available for this customer</p>
            <p className="text-sm">Please assign pricing structures to the customer first</p>
          </div>
        ) : (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {availablePricingStructures.map((structure) => (
                <div
                  key={structure.id}
                  onClick={() => setSelectedPricingStructure(structure)}
                  className={`border rounded-lg p-4 cursor-pointer transition-all hover:shadow-md ${
                    selectedPricingStructure?.id === structure.id
                      ? "border-primary bg-primary/5 ring-2 ring-primary/20"
                      : "border-gray-200 hover:border-gray-300"
                  }`}
                >
                  <div className="space-y-3">
                    <div className="flex items-start justify-between">
                      <div className="space-y-1">
                        <h3 className="font-medium text-base">{structure.name}</h3>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <div className="text-sm text-muted-foreground">
                                Product Code: {structure.productCode}
                              </div>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Internal product identifier</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </div>
                      {selectedPricingStructure?.id === structure.id && (
                        <CheckCircle className="h-5 w-5 text-primary shrink-0" />
                      )}
                    </div>

                    <div className="flex items-center gap-2">
                      <DollarSign className="h-4 w-4 text-muted-foreground shrink-0" />
                      {structure.specialPricing !== null && structure.specialPricing !== undefined ? (
                        <div className="space-y-1">
                          <Badge variant="secondary" className="bg-orange-50 text-orange-700 border-orange-200">
                            Special Price: {formatPrice(structure.specialPricing)}
                          </Badge>
                          <div className="text-xs text-muted-foreground line-through">
                            Standard: {formatPrice(structure.standardPricing)}
                          </div>
                        </div>
                      ) : (
                        <Badge variant="outline" className="bg-gray-50">
                          Standard Price: {formatPrice(structure.standardPricing)}
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
            
            {selectedPricingStructure && (
              <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <span className="text-sm font-medium text-green-800">
                    Selected: {selectedPricingStructure.name}
                    {selectedPricingStructure.specialPricing !== null && selectedPricingStructure.specialPricing !== undefined && 
                      ` (Special Price: ${formatPrice(selectedPricingStructure.specialPricing)})`}
                  </span>
                </div>
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
};

export default PricingStructureSelection;