import { useAtom } from "jotai";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { CheckCircle, XCircle, AlertCircle } from "lucide-react";

import {
  addressDiscrepancyAtom,
  isAddressDiscrepancyModalOpenAtom,
} from "@/jotai/orders";

interface AddressDiscrepancyModalProps {
  onResolve: (useSuggested: boolean) => void;
}

const AddressDiscrepancyModal = ({ onResolve }: AddressDiscrepancyModalProps) => {
  const [discrepancy] = useAtom(addressDiscrepancyAtom);
  const [isOpen, setIsOpen] = useAtom(isAddressDiscrepancyModalOpenAtom);

  const handleClose = () => {
    setIsOpen(false);
    // When closing without making a choice, treat it as rejecting the address
    onResolve(false);
  };

  const handleResolve = (useSuggested: boolean) => {
    onResolve(useSuggested);
    setIsOpen(false);
  };

  if (!discrepancy) return null;

  // Split addresses into components for comparison
  const original = {
    street: discrepancy.originalAddress.split(',')[0],
    city: discrepancy.originalAddress.split(',')[1]?.trim() || '',
    state: discrepancy.originalAddress.split(',')[2]?.trim()?.split(' ')[0] || '',
    zip: discrepancy.originalAddress.split(',')[2]?.trim()?.split(' ')[1] || ''
  };

  const suggested = {
    street: discrepancy.suggestedAddress.split(',')[0],
    city: discrepancy.suggestedAddress.split(',')[1]?.trim() || '',
    state: discrepancy.suggestedAddress.split(',')[2]?.trim()?.split(' ')[0] || '',
    zip: discrepancy.suggestedAddress.split(',')[2]?.trim()?.split(' ')[1] || ''
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <AlertCircle className="h-5 w-5 text-yellow-600" />
            <span>Address Discrepancy Found</span>
          </DialogTitle>
          <DialogDescription>
            The address you entered matches a property in our database, but with some differences.
            Please verify which address is correct.
          </DialogDescription>
        </DialogHeader>

        <div className="grid grid-cols-2 gap-6 my-4">
          <div className="space-y-2">
            <p className="font-medium text-sm text-gray-700">Your Entry:</p>
            <div className="p-3 border rounded-lg space-y-1">
              <p className="text-sm">{original.street}</p>
              <p className="text-sm">{original.city}, {original.state} {original.zip}</p>
            </div>
          </div>
          <div className="space-y-2">
            <p className="font-medium text-sm text-gray-700">Database Match:</p>
            <div className="p-3 border rounded-lg space-y-1 bg-yellow-50 border-yellow-200">
              <p className="text-sm">{suggested.street}</p>
              <p className="text-sm">{suggested.city}, {suggested.state} {suggested.zip}</p>
            </div>
          </div>
        </div>

        <div className="flex justify-end space-x-2 mt-4">
          <Button
            variant="outline"
            className="space-x-2 hover:bg-green-50 hover:border-green-200 hover:text-green-700"
            onClick={() => handleResolve(true)}
          >
            <CheckCircle className="h-4 w-4" />
            <span>Use Database Match</span>
          </Button>
          <Button
            variant="outline"
            className="space-x-2 hover:bg-red-50 hover:border-red-200 hover:text-red-700"
            onClick={() => handleResolve(false)}
          >
            <XCircle className="h-4 w-4" />
            <span>Remove the address</span>
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default AddressDiscrepancyModal; 