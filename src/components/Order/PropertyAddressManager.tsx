import { use<PERSON><PERSON> } from "jotai";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { 
  CheckCircle, 
  XCircle, 
  MapPin, 
  AlertTriangle,
  Loader2,
  Plus,
  Trash2,
  Upload
} from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";

import {
  propertyAddressesAtom,
  currentAddressInputAtom,
  isAddressValidAtom,
  isValidatingAddressAtom,
  addressSuggestionsAtom,
  isBatchUploadModalOpenAtom
} from "@/jotai/orders";
import BatchUploadModal from "@/components/Order/BatchUploadModal";
import AddressDiscrepancyModal from "@/components/Order/AddressDiscrepancyModal";

interface PropertyAddressManagerProps {
  handleAddressChange: (value: string) => void;
  handleAddressSuggestionSelect: (suggestion: string) => void;
  handleDiscrepancyResolution: (useSuggested: boolean) => void;
  addAddress: () => void;
  removeAddress: (index: number) => void;
}

const PropertyAddressManager = ({
  handleAddressChange,
  handleAddressSuggestionSelect,
  handleDiscrepancyResolution,
  addAddress,
  removeAddress
}: PropertyAddressManagerProps) => {
  const [propertyAddresses] = useAtom(propertyAddressesAtom);
  const [currentAddressInput] = useAtom(currentAddressInputAtom);
  const [isAddressValid] = useAtom(isAddressValidAtom);
  const [isValidatingAddress] = useAtom(isValidatingAddressAtom);
  const [addressSuggestions] = useAtom(addressSuggestionsAtom);
  const [isBatchUploadModalOpen, setIsBatchUploadModalOpen] = useAtom(isBatchUploadModalOpenAtom);

  // Validate address on input change with debounce
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    handleAddressChange(value);
    
    // Validation will be handled by the useAddressValidation hook
  };

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <MapPin className="h-5 w-5" />
            <span>Property Addresses</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Added Addresses List */}
          {propertyAddresses.length > 0 && (
            <div className="space-y-2">
              <Label>Added Addresses ({propertyAddresses.length})</Label>
              <div className="space-y-2 max-h-100 overflow-y-auto">
                {propertyAddresses.map((address, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-3 border rounded-lg bg-green-50/80 dark:bg-green-950/30 border-green-200 dark:border-green-900"
                  >
                    <div className="flex items-start space-x-2 flex-1">
                      <CheckCircle className="h-4 w-4 text-green-600 dark:text-green-400 mt-0.5 shrink-0" />
                      <span className="text-sm text-foreground">{address.fullAddress}</span>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => removeAddress(index)}
                      className="ml-2 h-8 w-8 p-0 border-red-200 dark:border-red-900 hover:bg-red-50 dark:hover:bg-red-950/50 hover:text-red-600 dark:hover:text-red-400"
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Address Input Options */}
          {propertyAddresses.length === 0 ? (
            <div className="space-y-6">
              {/* Header with explanation */}
              <div className="text-center space-y-2">
                <h3 className="text-lg font-medium">Add Property Addresses</h3>
                <p className="text-sm text-muted-foreground">
                  At least one property address is required to continue
                </p>
              </div>

              {/* Two options */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Manual Entry Option */}
                <div className="border rounded-lg p-6 space-y-4">
                  <div className="text-center space-y-2">
                    <div className="w-12 h-12 mx-auto bg-blue-100 rounded-lg flex items-center justify-center">
                      <Plus className="h-6 w-6 text-blue-600" />
                    </div>
                    <h4 className="font-medium">Manual Entry</h4>
                    <p className="text-sm text-muted-foreground">
                      Enter addresses one by one with real-time validation
                    </p>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="property-address">Property Address *</Label>
                    <div className="flex space-x-2">
                      <div className="relative flex-1">
                        <Input
                          id="property-address"
                          placeholder="Enter full address (e.g., 123 Main St, City, State ZIP)"
                          value={currentAddressInput}
                          onChange={handleInputChange}
                          className={`pr-10 ${
                            isAddressValid === true 
                              ? "border-green-500 focus-visible:ring-green-500" 
                              : isAddressValid === false 
                              ? "border-red-500 focus-visible:ring-red-500" 
                              : ""
                          }`}
                        />
                        <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                          {isValidatingAddress ? (
                            <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
                          ) : isAddressValid === true ? (
                            <CheckCircle className="h-4 w-4 text-green-500" />
                          ) : isAddressValid === false ? (
                            <XCircle className="h-4 w-4 text-red-500" />
                          ) : null}
                        </div>
                      </div>
                      <Button
                        onClick={addAddress}
                        disabled={!currentAddressInput.trim() || isAddressValid !== true}
                        className="space-x-1"
                      >
                        <Plus className="h-4 w-4" />
                        <span>Add</span>
                      </Button>
                    </div>
                    
                    {isAddressValid === true && currentAddressInput.trim() && (
                      <div className="text-xs text-green-600 flex items-center space-x-1">
                        <CheckCircle className="h-3 w-3" />
                        <span>Address validated - click Add to include</span>
                      </div>
                    )}
                    {isAddressValid === false && currentAddressInput.trim() && (
                      <div className="text-xs text-red-600 flex items-center space-x-1">
                        <XCircle className="h-3 w-3" />
                        <span>Invalid address - please check and try again</span>
                      </div>
                    )}
                  </div>
                </div>

                {/* Batch Upload Option */}
                <div className="border rounded-lg p-6 space-y-4">
                  <div className="text-center space-y-2">
                    <div className="w-12 h-12 mx-auto bg-green-100 rounded-lg flex items-center justify-center">
                      <Upload className="h-6 w-6 text-green-600" />
                    </div>
                    <h4 className="font-medium">Batch Upload</h4>
                    <p className="text-sm text-muted-foreground">
                      Upload multiple addresses from CSV or Excel file
                    </p>
                  </div>
                  
                  <div className="space-y-3">
                    <Button
                      variant="outline"
                      onClick={() => setIsBatchUploadModalOpen(true)}
                      className="w-full space-x-2"
                    >
                      <Upload className="h-4 w-4" />
                      <span>Upload File</span>
                    </Button>
                    
                    <div className="text-xs text-muted-foreground space-y-1">
                      <p>Supports CSV and Excel files</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            /* Add More Addresses - when addresses already exist */
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <Label className="text-base font-medium">Add More Addresses</Label>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setIsBatchUploadModalOpen(true)}
                  className="space-x-2"
                >
                  <Upload className="h-4 w-4" />
                  <span>Batch Upload</span>
                </Button>
              </div>
              
              <div className="flex space-x-2">
                <div className="relative flex-1">
                  <Input
                    placeholder="Enter full address (e.g., 123 Main St, City, State ZIP)"
                    value={currentAddressInput}
                    onChange={handleInputChange}
                    className={`pr-10 ${
                      isAddressValid === true 
                        ? "border-green-500 focus-visible:ring-green-500" 
                        : isAddressValid === false 
                        ? "border-red-500 focus-visible:ring-red-500" 
                        : ""
                    }`}
                  />
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                    {isValidatingAddress ? (
                      <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
                    ) : isAddressValid === true ? (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    ) : isAddressValid === false ? (
                      <XCircle className="h-4 w-4 text-red-500" />
                    ) : null}
                  </div>
                </div>
                <Button
                  onClick={addAddress}
                  disabled={!currentAddressInput.trim() || isAddressValid !== true}
                  className="space-x-1"
                >
                  <Plus className="h-4 w-4" />
                  <span>Add</span>
                </Button>
              </div>
              
              {isAddressValid === true && currentAddressInput.trim() && (
                <div className="text-xs text-green-600 flex items-center space-x-1">
                  <CheckCircle className="h-3 w-3" />
                  <span>Address validated - click Add to include</span>
                </div>
              )}
              {isAddressValid === false && currentAddressInput.trim() && (
                <div className="text-xs text-red-600 flex items-center space-x-1">
                  <XCircle className="h-3 w-3" />
                  <span>Invalid address - please check and try again</span>
                </div>
              )}
            </div>
          )}

          {/* Address Suggestions */}
          {isAddressValid === false && addressSuggestions.length > 0 && (
            <Alert className="border-orange-200 bg-orange-50">
              <AlertTriangle className="h-4 w-4 text-orange-600" />
              <AlertDescription className="text-orange-800">
                <div className="space-y-2">
                  <p>Address not found. Did you mean one of these?</p>
                  <div className="space-y-1">
                    {addressSuggestions.map((suggestion, index) => (
                      <button
                        key={index}
                        onClick={() => handleAddressSuggestionSelect(suggestion)}
                        className="block w-full text-left p-2 rounded border border-orange-200 hover:bg-orange-100 transition-colors"
                      >
                        {suggestion}
                      </button>
                    ))}
                  </div>
                </div>
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Batch Upload Modal */}
      <BatchUploadModal
        isOpen={isBatchUploadModalOpen}
        onClose={() => setIsBatchUploadModalOpen(false)}
      />
      
      <AddressDiscrepancyModal
        onResolve={handleDiscrepancyResolution}
      />
    </>
  );
};

export default PropertyAddressManager;