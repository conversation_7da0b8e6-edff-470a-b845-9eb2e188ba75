import { use<PERSON>tom } from "jotai";
import { useState } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import { ConfidenceScore } from "@/components/ui/confidence-score";
import {
  CheckCircle,
  ArrowLeft,
  User,
  Building2,
  Mail,
  Phone,
  MapPin,
  Package,
  FileText,
  CreditCard,
  Loader2,
  AlertTriangle,
} from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";

import { orderDataAtom, OrderPayload, orderStepAtom, resetOrderAtom } from "@/jotai/orders";
import { postCreateOrder } from "@/services/data";

const OrderReview = () => {
  const [orderData] = useAtom(orderDataAtom);
  const [, setOrderStep] = useAtom(orderStepAtom);
  const [, resetOrder] = useAtom(resetOrderAtom);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);

  // Format price consistently
  const formatPrice = (price: string | number | null) => {
    if (price === null || price === undefined) return "N/A";
    const numPrice = typeof price === "string" ? parseFloat(price) : price;
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      maximumFractionDigits: 0,
    }).format(numPrice);
  };

  const handleBackStep = () => {
    setOrderStep(3);
  };

  const handlePlaceOrder = async () => {
    if (!orderData.customer || !orderData.pricingStructure) {
      setSubmitError("Missing required order data");
      return;
    }

    setIsSubmitting(true);
    setSubmitError(null);

    try {
      // Extract state code from address (simple extraction for demo)
      const extractStateCode = (address: string): string => {
        // Look for pattern like ", TX " or ", CA " in the address
        const stateMatch = address.match(/,\s*([A-Z]{2})\s+\d{5}/);
        return stateMatch ? stateMatch[1] : "TX"; // Default to TX if not found
      };

      // Create the order object in the specified format
      const orderPayload: OrderPayload = {
        clientCustomerId: orderData.customer.id ? orderData.customer.id.toString() : undefined,
        pricingStructureId: orderData.pricingStructure.id.toString(),
        properties: orderData.propertyAddresses?.filter((address) => address.propertyId).map((address) => ({
          propertyAddress: address.fullAddress,
          propertyStateCode: extractStateCode(address.fullAddress),
          propertyId: address.propertyId!,
        })) || [],
        isActive: true,
        isTestData: orderData.customer.isTestData || false,
        payload: {
          isVueitNeeded: orderData.isVueitNeeded || false,
          propertyValuations: orderData.propertyAddresses?.filter((address) => address.propertyId && address.valuationData).map((address) => ({
            propertyId: address.propertyId!,
            valuationData: {
              estimatedValue: address.valuationData!.estimatedValue,
              confidenceScore: address.valuationData!.confidenceScore,
              statusMessage: address.valuationData!.statusMessage
            }
          }))
        }
      };

      // Log the order object for debugging
      console.log("Order Payload:", orderPayload);

      // Submit the order via API
      const response = await postCreateOrder(orderPayload);
      console.log("Order created successfully:", response);

      // Show success toast and reset
      toast.success("Order placed successfully!", {
        description: "Your order has been submitted and is being processed.",
        duration: 5000,
      });
      resetOrder();
    } catch (error) {
      console.error("Failed to place order:", error);
      setSubmitError(error instanceof Error ? error.message : "Failed to place order");
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!orderData.customer || !orderData.pricingStructure) {
    return null;
  }

  const { customer, pricingStructure, propertyAddresses, customerOrderNumber, email, phone } =
    orderData;

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Review Order</h2>
          <p className="text-muted-foreground">Please review all order details before confirming</p>
        </div>
        <Button
          variant="outline"
          onClick={handleBackStep}
          disabled={isSubmitting}
          className="space-x-2"
        >
          <ArrowLeft className="h-4 w-4" />
          <span>Back to Edit</span>
        </Button>
      </div>

      {/* Error Alert */}
      {submitError && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{submitError}</AlertDescription>
        </Alert>
      )}

      <Alert className="border-blue-200 bg-blue-50">
        <CheckCircle className="h-4 w-4 text-blue-600" />
        <AlertDescription className="text-blue-800">
          Please carefully review all information below. Once confirmed, the order will be
          processed.
        </AlertDescription>
      </Alert>

      {/* Customer Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Building2 className="h-5 w-5" />
            <span>Customer Information</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div>
                <h3 className="font-semibold text-lg">{customer.company}</h3>
                <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                  <span>ID: {customer.id}</span>
                  <span>Account: {customer.sourceAcctNo}</span>
                  <Badge variant={customer.isActiveFlag ? "default" : "secondary"}>
                    {customer.isActiveFlag ? "Active" : "Inactive"}
                  </Badge>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex items-center space-x-2 text-sm">
                  <User className="h-4 w-4 text-muted-foreground" />
                  <span>{customer.contactName || "No contact name"}</span>
                </div>
                <div className="flex items-center space-x-2 text-sm">
                  <Mail className="h-4 w-4 text-muted-foreground" />
                  <span>{customer.email}</span>
                </div>
                <div className="flex items-center space-x-2 text-sm">
                  <Phone className="h-4 w-4 text-muted-foreground" />
                  <span>
                    {customer.phone}
                    {customer.extn && ` ext. ${customer.extn}`}
                  </span>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <h4 className="font-medium text-sm text-muted-foreground mb-2">Billing Address</h4>
                <div className="flex items-start space-x-2">
                  <MapPin className="h-4 w-4 text-muted-foreground mt-0.5 shrink-0" />
                  <div className="text-sm">
                    <div>{customer.bAddress1}</div>
                    {customer.bAddress2 && <div>{customer.bAddress2}</div>}
                    <div>
                      {customer.bCity}, {customer.bState} {customer.bZip}
                    </div>
                    <div>{customer.county} County</div>
                  </div>
                </div>
              </div>

              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Billable:</span>
                  <Badge variant={customer.isBillable ? "default" : "secondary"}>
                    {customer.isBillable ? "Yes" : "No"}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Taxable:</span>
                  <Badge variant={customer.isTaxableFlag ? "default" : "secondary"}>
                    {customer.isTaxableFlag ? "Yes" : "No"}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Credit Card:</span>
                  <Badge variant={customer.creditCardFlag ? "default" : "secondary"}>
                    {customer.creditCardFlag ? "Yes" : "No"}
                  </Badge>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Order Details */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Package className="h-5 w-5" />
            <span>Order Details</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Product/Service */}
          <div>
            <h4 className="font-medium text-sm text-muted-foreground mb-2">Pricing Structure</h4>
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div className="space-y-2">
                <h3 className="font-semibold">{pricingStructure.name}</h3>
                <div className="flex items-center gap-2">
                  <Badge variant="secondary">{pricingStructure.productCode}</Badge>
                  {pricingStructure.specialPricing !== null &&
                  pricingStructure.specialPricing !== undefined ? (
                    <Badge
                      variant="outline"
                      className="bg-orange-50 text-orange-700 border-orange-200"
                    >
                      Special Price: {formatPrice(pricingStructure.specialPricing)}
                    </Badge>
                  ) : (
                    <Badge variant="outline" className="bg-gray-50">
                      Standard Price: {formatPrice(pricingStructure.standardPricing)}
                    </Badge>
                  )}
                </div>
              </div>
            </div>
          </div>

          <Separator />

          {/* Contact Information */}
          <div>
            <h4 className="font-medium text-sm text-muted-foreground mb-2">
              Order Contact Information
            </h4>
            <div className="space-y-2 p-4 border rounded-lg">
              <div className="flex items-center space-x-2">
                <Mail className="h-4 w-4 text-muted-foreground" />
                <span>{email}</span>
              </div>
              <div className="flex items-center space-x-2">
                <Phone className="h-4 w-4 text-muted-foreground" />
                <span>{phone}</span>
              </div>
            </div>
          </div>

          <Separator />

          {/* Property Addresses */}
          <div>
            <h4 className="font-medium text-sm text-muted-foreground mb-2">
              Property Addresses ({propertyAddresses?.length || 0})
            </h4>
            <div className="space-y-2">
              {propertyAddresses?.map((address, index) => (
                <div key={index} className="flex items-start space-x-2 p-4 border rounded-lg bg-green-50/80 dark:bg-green-950/30 border-green-200 dark:border-green-900">
                  <MapPin className="h-4 w-4 text-green-600 dark:text-green-400 mt-0.5 shrink-0" />
                  <div className="w-full">
                    <div className="font-medium text-foreground">{address.fullAddress}</div>
                    <div className="flex items-center flex-wrap gap-1 text-sm text-green-600 dark:text-green-400 mt-1">
                      <CheckCircle className="h-3 w-3" />
                      <span>Address verified</span>
                      {address.propertyId && (
                        <>
                          <span>•</span>
                          <span>Property Id: {address.propertyId}</span>
                        </>
                      )}
                      {address.valuationData && (
                        address.valuationData.estimatedValue === null && 
                        address.valuationData.confidenceScore === null ? (
                          <>
                            <span>•</span>
                            <span className="italic">{address.valuationData.statusMessage}</span>
                          </>
                        ) : (
                          <>
                            <span>•</span>
                            <span>Estimated Value: {formatPrice(address.valuationData.estimatedValue)}</span>
                            <span>•</span>
                            <span className="flex items-center gap-1">
                              Confidence: <ConfidenceScore score={address.valuationData.confidenceScore} size="sm" />
                            </span>
                          </>
                        )
                      )}
                    </div>
                  </div>
                </div>
              )) || (
                <div className="text-muted-foreground text-sm">No addresses added</div>
              )}
            </div>
          </div>

          {/* Customer Order Number */}
          {customerOrderNumber && (
            <>
              <Separator />
              <div>
                <h4 className="font-medium text-sm text-muted-foreground mb-2">
                  Customer Order Number
                </h4>
                <div className="flex items-center space-x-2 p-4 border rounded-lg">
                  <FileText className="h-4 w-4 text-muted-foreground" />
                  <span className="font-mono">{customerOrderNumber}</span>
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* Order Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <CreditCard className="h-5 w-5" />
            <span>Order Summary</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span>Pricing Structure:</span>
              <span>{pricingStructure.name}</span>
            </div>
            <div className="flex justify-between font-medium">
              <span>Price per Property:</span>
              <span
                className={
                  pricingStructure.specialPricing !== null &&
                  pricingStructure.specialPricing !== undefined
                    ? "text-orange-600"
                    : ""
                }
              >
                {pricingStructure.specialPricing !== null &&
                pricingStructure.specialPricing !== undefined
                  ? formatPrice(pricingStructure.specialPricing)
                  : formatPrice(pricingStructure.standardPricing)}
              </span>
            </div>
            <div className="flex justify-between text-sm text-muted-foreground">
              <span>Number of Properties:</span>
              <span>{propertyAddresses?.length || 0}</span>
            </div>
            {pricingStructure.specialPricing !== null &&
              pricingStructure.specialPricing !== undefined && (
                <div className="flex justify-between text-sm text-orange-600">
                  <span>Pricing Type:</span>
                  <span>Special Pricing Applied</span>
                </div>
              )}
            <div className="flex justify-between text-sm text-muted-foreground">
              <span>Taxable:</span>
              <span>{customer.isTaxableFlag ? "Yes" : "No"}</span>
            </div>
            <Separator />
            <div className="flex justify-between font-semibold text-lg">
              <span>Total Estimated Price:</span>
              <span>
                {formatPrice(
                  (propertyAddresses?.length || 0) *
                    (pricingStructure.specialPricing !== null &&
                    pricingStructure.specialPricing !== undefined
                      ? parseFloat(pricingStructure.specialPricing.toString())
                      : parseFloat(pricingStructure.standardPricing))
                )}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Confirm Order */}
      <div className="flex justify-between items-center">
        <div className="text-sm text-muted-foreground">
          By clicking "Place Order", you confirm that all information is correct
        </div>
        <Button
          onClick={handlePlaceOrder}
          disabled={isSubmitting}
          size="lg"
          className="bg-green-600 hover:bg-green-700 text-white space-x-2 disabled:opacity-50"
        >
          {isSubmitting ? (
            <>
              <Loader2 className="h-4 w-4 animate-spin" />
              <span>Placing Order...</span>
            </>
          ) : (
            <>
              <CheckCircle className="h-4 w-4" />
              <span>Place Order</span>
            </>
          )}
        </Button>
      </div>
    </div>
  );
};

export default OrderReview;
