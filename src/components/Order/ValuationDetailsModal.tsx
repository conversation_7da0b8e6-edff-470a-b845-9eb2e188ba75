import {
  Dialog,
  Di<PERSON><PERSON>ontent,
  Di<PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";
import { APIOrder } from "@/services/data";
import { ConfidenceScore } from "@/components/ui/confidence-score";

interface ValuationDetailsModalProps {
  order: APIOrder;
  isOpen: boolean;
  onClose: () => void;
}

const formatCurrency = (value: number | null) => {
  if (value === null) return "N/A";
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
    maximumFractionDigits: 0,
  }).format(value);
};

const ValuationDetailsModal: React.FC<ValuationDetailsModalProps> = ({
  order,
  isOpen,
  onClose,
}) => {
  console.log("Modal Order:", order);
  console.log("Modal Order Payload:", order.payload);
  console.log("Modal Order Property Valuations:", order.payload?.propertyValuations);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Property Valuations - Order #{order.orderNumber}</DialogTitle>
        </DialogHeader>
        <ScrollArea className="max-h-[60vh]">
          <div className="space-y-4 p-1">
            {order.properties.map((property, index) => {
              console.log("Property:", property);
              const valuation = order.payload?.propertyValuations?.find(
                (v) => {
                  console.log("Comparing:", v.propertyId, property.propertyId);
                  return v.propertyId === property.propertyId;
                }
              )?.valuationData;
              console.log("Found valuation:", valuation);

              return (
                <div
                  key={property.propertyId}
                  className="rounded-lg border p-4 space-y-2"
                >
                  <h3 className="font-medium">
                    Property {index + 1}: {property.propertyAddress}
                  </h3>
                  {valuation ? (
                    <div className="space-y-1 text-sm">
                      <div className="grid grid-cols-2 gap-4 mt-4">
                        <div>
                          <div className="text-sm font-medium mb-2">Confidence Score</div>
                          <ConfidenceScore score={valuation.confidenceScore} size="lg" />
                        </div>
                        <div>
                          <span className="text-gray-500">Estimated Value:</span>
                          <div>
                            {formatCurrency(valuation.estimatedValue)}
                          </div>
                        </div>
                      </div>
                      {valuation.statusMessage && (
                        <div className="mt-2">
                          <span className="text-gray-500">Status:</span>
                          <div>{valuation.statusMessage}</div>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="text-sm text-gray-500 italic">
                      No valuation data available
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
};

export default ValuationDetailsModal; 