import { use<PERSON><PERSON> } from "jotai";
import { useEffect, useState } from "react";
import { Search, Plus, User, Building2 } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";

import {
  customerSearchTermAtom,
  customerSearchResultsAtom,
  isSearchingCustomersAtom,
  customerSearchErrorAtom,
  selectedOrderCustomerAtom,
  orderStepAtom,
} from "@/jotai/orders";
import { isCreateModalOpenAtom } from "@/jotai/customers";
import { getAllCustomers } from "@/services/data";
import { ClientCustomer } from "@/components/Customer/types";

const CustomerSearch = () => {
  const [searchTerm, setSearchTerm] = useAtom(customerSearchTermAtom);
  const [searchResults, setSearchResults] = useAtom(customerSearchResultsAtom);
  const [isSearching, setIsSearching] = useAtom(isSearchingCustomersAtom);
  const [searchError, setSearchError] = useAtom(customerSearchErrorAtom);
  const [, setSelectedCustomer] = useAtom(selectedOrderCustomerAtom);
  const [, setOrderStep] = useAtom(orderStepAtom);
  const [, setIsCreateModalOpen] = useAtom(isCreateModalOpenAtom);

  const [allCustomers, setAllCustomers] = useState<ClientCustomer[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);

  // Load all customers on component mount
  useEffect(() => {
    const loadCustomers = async () => {
      try {
        setIsSearching(true);
        setSearchError(null);
        const customers = await getAllCustomers();
        setAllCustomers(customers);
      } catch (error) {
        setSearchError(error instanceof Error ? error.message : "Failed to load customers");
      } finally {
        setIsSearching(false);
      }
    };

    loadCustomers();
  }, []);

  // Filter customers based on search term
  useEffect(() => {
    if (!searchTerm.trim()) {
      setSearchResults([]);
      setShowSuggestions(false);
      return;
    }

    const filtered = allCustomers.filter(
      (customer) => 
        customer.isActiveFlag && (
          customer.company?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          (customer.id && customer.id.toString().includes(searchTerm)) ||
          (customer.sourceAcctNo && customer.sourceAcctNo.toString().includes(searchTerm)) ||
          customer.email?.toLowerCase().includes(searchTerm.toLowerCase())
        )
    );

    setSearchResults(filtered);
    setShowSuggestions(true);
  }, [searchTerm, allCustomers]);

  const handleCustomerSelect = (customer: ClientCustomer) => {
    setSelectedCustomer(customer);
    setSearchTerm(customer.company);
    setShowSuggestions(false);
    setOrderStep(2); // Move to next step
  };

  const handleCreateNewCustomer = () => {
    setIsCreateModalOpen(true);
  };

  const handleInputChange = (value: string) => {
    setSearchTerm(value);
    if (!value.trim()) {
      setShowSuggestions(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <div className="text-center space-y-2">
          <h2 className="text-xl font-semibold tracking-tight">Create New Order</h2>
          <p className="text-muted-foreground text-sm">
            Search for an existing customer or create a new one to start the order process
          </p>
        </div>

        {searchError && (
          <Alert variant="destructive">
            <AlertDescription>{searchError}</AlertDescription>
          </Alert>
        )}

        <div className="relative max-w-md mx-auto">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground text-xs" />
            <Input
              placeholder="Search by company name, ID, account number, or email..."
              value={searchTerm}
              onChange={(e) => handleInputChange(e.target.value)}
              className="pl-10 pr-4 text-xs"
              disabled={isSearching}
            />
          </div>

          {showSuggestions && (
            <Card className="absolute top-full left-0 right-0 mt-1 z-50 max-h-80 overflow-y-auto shadow-lg">
              <CardContent className="p-0">
                {searchResults.length > 0 ? (
                  <div className="space-y-1">
                    {searchResults.slice(0, 10).map((customer) => (
                      <div
                        key={customer.id}
                        onClick={() => handleCustomerSelect(customer)}
                        className="p-3 hover:bg-accent cursor-pointer border-b last:border-b-0 transition-colors"
                      >
                        <div className="flex items-start justify-between text-xs">
                          <div className="space-y-1 flex-1">
                            <div className="flex items-center space-x-2">
                              <Building2 className="h-4 w-4 text-muted-foreground" />
                              <span className="font-medium">{customer.company}</span>
                            </div>
                            <div className="text-sm text-muted-foreground">
                              <div className="flex items-center space-x-4">
                                <span>ID: {customer.id}</span>
                                <span>Acct: {customer.sourceAcctNo}</span>
                              </div>
                              <div className="flex items-center space-x-2 mt-1">
                                <User className="h-3 w-3" />
                                <span>{customer.email}</span>
                              </div>
                            </div>
                          </div>
                          <div className="space-y-1">
                            <Badge variant={customer.isActiveFlag ? "default" : "secondary"}>
                              {customer.isActiveFlag ? "Active" : "Inactive"}
                            </Badge>
                            {customer.isTestData && (
                              <Badge variant="outline" className="text-xs">
                                Test
                              </Badge>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="p-4 text-center space-y-3">
                    <p className="text-muted-foreground">No customers found matching your search</p>
                    <Button
                      onClick={handleCreateNewCustomer}
                      variant="outline"
                      size="sm"
                      className="space-x-2"
                    >
                      <Plus className="h-4 w-4" />
                      <span>Create New Customer</span>
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </div>

        {searchTerm && !showSuggestions && searchResults.length === 0 && (
          <div className="text-center space-y-3">
            <p className="text-muted-foreground">No customers found</p>
            <Button
              onClick={handleCreateNewCustomer}
              variant="outline"
              className="space-x-2"
            >
              <Plus className="h-4 w-4" />
              <span>Create New Customer</span>
            </Button>
          </div>
        )}

        <div className="flex justify-center">
          <Button
            onClick={handleCreateNewCustomer}
            variant="ghost"
            className="space-x-2 text-muted-foreground hover:text-foreground"
          >
            <Plus className="h-4 w-4" />
            <span className="text-sm">Or create a new customer</span>
          </Button>
        </div>
      </div>

      {isSearching && (
  <div className="text-center">
    <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
  </div>
)}
    </div>
  );
};

export default CustomerSearch;