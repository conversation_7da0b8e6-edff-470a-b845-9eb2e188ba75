import { useAtom } from "jotai";
import { useEffect } from "react";
import { Button } from "@/components/ui/button";
import { ArrowRight, ArrowLeft } from "lucide-react";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";

import {
  selectedOrderCustomerAtom,
  selectedPricingStructureAtom,
  propertyAddressesAtom,
  customerOrderNumberAtom,
  orderEmailAtom,
  orderPhoneAtom,
  orderStepAtom,
  orderDataAtom,
  isVueitNeededAtom,
} from "@/jotai/orders";

import PricingStructureSelection from "@/components/Order/PricingStructureSelection";
import PropertyAddressManager from "@/components/Order/PropertyAddressManager";
import OrderContactForm from "@/components/Order/OrderContactForm";
import { useAddressValidation } from "./hooks/useAddressValidation";
import CustomerOrderNumberForm from "./CustomerOrderNumberForm";


const OrderConfiguration = () => {
  const [selectedCustomer] = useAtom(selectedOrderCustomerAtom);
  const [selectedPricingStructure] = useAtom(selectedPricingStructureAtom);
  const [propertyAddresses] = useAtom(propertyAddressesAtom);
  const [customerOrderNumber] = useAtom(customerOrderNumberAtom);
  const [orderEmail, setOrderEmail] = useAtom(orderEmailAtom);
  const [orderPhone, setOrderPhone] = useAtom(orderPhoneAtom);
  const [, setOrderStep] = useAtom(orderStepAtom);
  const [, setOrderData] = useAtom(orderDataAtom);
  const [isVueitNeeded, setIsVueitNeeded] = useAtom(isVueitNeededAtom);

  // Address validation hooks
  const {
    handleAddressChange,
    handleAddressSuggestionSelect,
    handleDiscrepancyResolution,
    addAddress,
    removeAddress,
  } = useAddressValidation();

  // Pre-fill email and phone from customer data
  useEffect(() => {
    if (selectedCustomer && !orderEmail && !orderPhone) {
      setOrderEmail(selectedCustomer.email || "");
      setOrderPhone(selectedCustomer.phone || "");
    }
  }, [selectedCustomer, orderEmail, orderPhone, setOrderEmail, setOrderPhone]);

  const canProceed = () => {
    return selectedPricingStructure && 
           propertyAddresses.length > 0 &&
           orderEmail.trim() &&
           orderPhone.trim();
  };

  const handleNextStep = () => {
    if (canProceed() && selectedCustomer) {
      setOrderData({
        customer: selectedCustomer,
        pricingStructure: selectedPricingStructure,
        propertyAddresses,
        customerOrderNumber: customerOrderNumber || undefined,
        email: orderEmail,
        phone: orderPhone,
        isVueitNeeded
      });
      setOrderStep(4); // Move to review step
    }
  };

  const handleBackStep = () => {
    setOrderStep(2);
  };

  if (!selectedCustomer) {
    return null;
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Configure Order</h2>
          <p className="text-muted-foreground">
            Select product and enter property details for {selectedCustomer.company}
          </p>
        </div>
        <Button 
          variant="outline" 
          onClick={handleBackStep}
          className="space-x-2"
        >
          <ArrowLeft className="h-4 w-4" />
          <span>Back</span>
        </Button>
      </div>

      {/* Product Selection */}
      <PricingStructureSelection />

      {/* Property Addresses */}
      <PropertyAddressManager
        handleAddressChange={handleAddressChange}
        handleAddressSuggestionSelect={handleAddressSuggestionSelect}
        handleDiscrepancyResolution={handleDiscrepancyResolution}
        addAddress={addAddress}
        removeAddress={removeAddress}
      />

      {/* Contact Information */}
      <OrderContactForm />

      {/* Customer Order Number */}
      <CustomerOrderNumberForm />

      {/* iVueit Checkbox */}
      <div className="space-y-4">
        <div className="flex items-center space-x-2">
          <Checkbox
            id="isVueitNeeded"
            checked={isVueitNeeded}
            onCheckedChange={(checked) => setIsVueitNeeded(checked as boolean)}
          />
          <Label htmlFor="isVueitNeeded">iVueit needed</Label>
        </div>
      </div>

      {/* Continue Button */}
      <div className="flex justify-end">
        <Button 
          onClick={handleNextStep}
          disabled={!canProceed()}
          className="space-x-2"
          size="lg"
        >
          <span>Continue to Review</span>
          <ArrowRight className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
};

export default OrderConfiguration;