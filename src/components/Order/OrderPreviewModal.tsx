import { useState, useEffect, useRef } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Loader2, CheckCircle } from "lucide-react";
import { APIOrder, getBPOPDFInfo, updateOrder } from "@/services/data";
import { toast } from "sonner";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";

interface ComparableProperty {
  address: string;
  city: string;
  state: string;
  zipcode: string;
  county: string;
  proximity: number;
  salePrice: number;
  pricePerSqFt: number;
  originalListingPrice: number;
  currentListingPrice: number;
  saleDate: string | null;
  listDate: string;
  daysOnMarket: number;
  mlsID: string;
  financing: string | null;
  salesConcession: string | null;
  bankORREOSale: string | null;
  location: string;
  siteORView: string | null;
  siteORLotSize: number;
  yearBuilt: number;
  construction: string | null;
  condition: string | null;
  style: string;
  totalRooms: number;
  bedrooms: number;
  bathrooms: number;
  grossLivingArea: number;
  basement: string;
  heating: string;
  cooling: string;
  garage: string;
  carport: string | null;
  additionalAmenities: string | null;
}

interface PropertyDetails {
  fullAddress: string;
  status: string | null;
  longitude: string;
  latitude: string;
  placekey: string | null;
  orderInformation: {
    address: string;
    city: string;
    state: string;
    zipcode: string;
    county: string;
    parcelID: string;
    feeSimpleORLeasehold: string | null;
  };
  propertyInformation: {
    numberOfUnits: number;
    propertyType: string;
    propertyStyle: string;
    sqftGLA: number;
    totalRooms: number;
    bedrooms: number;
    bathrooms: number;
    garageSpaces: number;
    garage: string;
    yearBuilt: number;
    view: string;
    pool: string;
    spa: string | null;
    featurePorch: string;
    featurePatio: string;
    featureDeck: string;
    numberOfFireplaces: number;
    overallCondition: string;
    occupancy: string;
    currentRent: number | null;
    marketRent: number | null;
    isListed: string;
    isListedInPast12Months: string | null;
    listPrice: number;
    nameOfListingCompany: string;
    listingAgentPhone: string;
    isTransferredInPast12Months: string | null;
    priorSaleDate: string;
    priorSalePrice: number;
    currentTax: number;
    delinquentTax: number | null;
    condoORPUD: string | null;
    feeHOA: number | null;
    zoning: string;
    lotSize: number;
    landValue: number;
    isConformsToNeighborhood: string | null;
  };
  conditionInformation: any | null;
  neighborhoodInformation: {
    marketConditions: string | null;
    location: string;
  };
  activeComparablePropertyInformationList: ComparableProperty[];
  closedComparablePropertyInformationList: ComparableProperty[];
}

interface OrderPreviewModalProps {
  order: APIOrder;
  isOpen: boolean;
  onClose: () => void;
  onPropertySigned?: (orderId: number, propertyId: string) => void;
}

interface CachedPropertyData {
  original: PropertyDetails;
  edited: PropertyDetails;
  timestamp: number;
}

// Remove mock API call
// const fetchPropertyDetails = async (propertyId: string): Promise<PropertyDetails> => {
//   // Simulate API delay
//   await new Promise(resolve => setTimeout(resolve, 1000));
//
//   // Return mock data
//   return {
//     ...
//   };
// };

const formatPrice = (price: number | null) => {
  if (price === null) return "N/A";
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
  }).format(price);
};

const formatDate = (date: string | null) => {
  if (!date) return "N/A";
  return new Date(date).toLocaleDateString();
};

const fieldNameMap: { [key: string]: string } = {
  feeSimpleORLeasehold: "Fee Simple OR Leasehold",
  siteORView: "Site OR View",
  bankORREOSale: "Bank OR REO Sale",
  siteORLotSize: "Site OR Lot Size",
  mlsID: "MLS ID",
};

const formatFieldName = (name: string) => {
  if (fieldNameMap[name]) {
    return fieldNameMap[name];
  }

  const result = name
    .replace(/([a-z0-9])([A-Z])/g, "$1 $2")
    .replace(/([A-Z])([A-Z][a-z])/g, "$1 $2");

  return result.charAt(0).toUpperCase() + result.slice(1);
};

const isPrice = (key: string, value: any): boolean => {
  const priceFields = [
    "listPrice",
    "priorSalePrice",
    "currentRent",
    "marketRent",
    "landValue",
    "feeHOA",
    "currentTax",
    "delinquentTax",
    "salePrice",
    "pricePerSqFt",
    "originalListingPrice",
    "currentListingPrice",
  ];
  return priceFields.includes(key) && typeof value === "number";
};

const ComparablePropertyCard: React.FC<{ property: ComparableProperty }> = ({ property }) => (
  <div className="border rounded-lg p-4 mb-4">
    <div className="grid grid-cols-2 gap-4">
      <div>
        <h4 className="font-medium">{property.address}</h4>
        <p className="text-sm text-muted-foreground">
          {property.city}, {property.state} {property.zipcode}
        </p>
      </div>
      <div className="text-right">
        <p className="font-medium">{formatPrice(property.salePrice)}</p>
        <p className="text-sm text-muted-foreground">{formatPrice(property.pricePerSqFt)}/sqft</p>
      </div>
    </div>
    <Separator className="my-4" />
    <div className="grid grid-cols-3 gap-4 text-sm">
      <div>
        <Label>Sale Date</Label>
        <div>{formatDate(property.saleDate)}</div>
      </div>
      <div>
        <Label>List Date</Label>
        <div>{formatDate(property.listDate)}</div>
      </div>
      <div>
        <Label>Days on Market</Label>
        <div>{property.daysOnMarket}</div>
      </div>
      <div>
        <Label>Year Built</Label>
        <div>{property.yearBuilt}</div>
      </div>
      <div>
        <Label>Lot Size</Label>
        <div>{property.siteORLotSize} acres</div>
      </div>
      <div>
        <Label>Living Area</Label>
        <div>{property.grossLivingArea} sqft</div>
      </div>
      <div>
        <Label>Bedrooms</Label>
        <div>{property.bedrooms}</div>
      </div>
      <div>
        <Label>Bathrooms</Label>
        <div>{property.bathrooms}</div>
      </div>
      <div>
        <Label>Style</Label>
        <div>{property.style}</div>
      </div>
    </div>
  </div>
);

const OrderPreviewModal: React.FC<OrderPreviewModalProps> = ({
  order,
  isOpen,
  onClose,
  onPropertySigned,
}) => {
  const [selectedPropertyId, setSelectedPropertyId] = useState<string>("");
  const [loading, setLoading] = useState(false);
  const [editedDetails, setEditedDetails] = useState<PropertyDetails | null>(null);
  const [isConfirmingSign, setIsConfirmingSign] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [localOrder, setLocalOrder] = useState(order);

  // Debug effect to track editedDetails changes
  useEffect(() => {
    console.log("editedDetails changed:", {
      selectedPropertyId,
      hasEditedDetails: !!editedDetails,
      address: editedDetails?.fullAddress,
      timestamp: Date.now(),
    });
  }, [editedDetails, selectedPropertyId]);

  // Cache to store property details for each propertyId
  const propertyCache = useRef<Map<string, CachedPropertyData>>(new Map());

  // Cache expiration time (30 minutes)
  const CACHE_EXPIRATION_MS = 30 * 60 * 1000;

  // Helper function to get the sign data from payload.sign (flat structure)
  const getSignData = () => {
    return localOrder.payload?.sign || {};
  };

  // Calculate signed count based on payload.sign field and properties array
  const getSignedCount = () => {
    const signData = getSignData();
    const payloadSignedIds = Object.keys(signData).filter((id) => signData[id] === true);
    const propertiesSignedCount = localOrder.properties.filter((p) => p.signed).length;

    // Use the higher count to ensure accuracy (in case data is inconsistent)
    return Math.max(payloadSignedIds.length, propertiesSignedCount);
  };

  // Check if a specific property is signed based on both payload.sign and properties array
  const isPropertySigned = (propertyId: string): boolean => {
    const signData = getSignData();
    const payloadSigned = signData[propertyId] === true;
    const propertySigned =
      localOrder.properties.find((p) => p.propertyId === propertyId)?.signed === true;

    // Return true if either source indicates it's signed
    return payloadSigned || propertySigned;
  };

  const signedCount = getSignedCount();
  const totalProperties = localOrder.properties.length;

  // Update localOrder when the order prop changes
  useEffect(() => {
    setLocalOrder(order);
  }, [order]);

  // Clear cache when modal closes to prevent stale data between different orders
  useEffect(() => {
    if (!isOpen) {
      console.log(`Modal closed, clearing cache. Had ${propertyCache.current.size} entries`);
      propertyCache.current.clear();
    } else {
      console.log(`Modal opened for order ${order.id}`);
    }
  }, [isOpen, order.id]);

  useEffect(() => {
    if (order.properties.length > 0 && !selectedPropertyId) {
      setSelectedPropertyId(order.properties[0].propertyId);
    }
  }, [order.properties, selectedPropertyId]);

  // Check if cached data is still valid
  const isCacheValid = (cachedData: CachedPropertyData): boolean => {
    const now = Date.now();
    return now - cachedData.timestamp < CACHE_EXPIRATION_MS;
  };

  // Clear cache for a specific property
  const clearPropertyCache = (propertyId: string) => {
    propertyCache.current.delete(propertyId);
  };

  useEffect(() => {
    const loadPropertyDetails = async () => {
      if (!selectedPropertyId) return;

      // Check cache first
      const cachedData = propertyCache.current.get(selectedPropertyId);
      console.log(`Cache check for property ${selectedPropertyId}:`, {
        hasCachedData: !!cachedData,
        cacheSize: propertyCache.current.size,
        cacheKeys: Array.from(propertyCache.current.keys()),
        isValid: cachedData ? isCacheValid(cachedData) : false,
        cacheAge: cachedData ? Date.now() - cachedData.timestamp : null,
        cacheAgeMinutes: cachedData
          ? Math.round(((Date.now() - cachedData.timestamp) / 60000) * 100) / 100
          : null,
        expirationMs: CACHE_EXPIRATION_MS,
        expirationMinutes: CACHE_EXPIRATION_MS / 60000,
      });

      if (cachedData && isCacheValid(cachedData)) {
        console.log(`Loading property ${selectedPropertyId} from cache`);
        console.log("Cache data being loaded:", {
          propertyId: selectedPropertyId,
          address: cachedData.edited.fullAddress,
          hasOrderInfo: !!cachedData.edited.orderInformation,
          hasPropertyInfo: !!cachedData.edited.propertyInformation,
        });

        // Create a fresh copy to ensure React detects the change
        const freshCopy = JSON.parse(JSON.stringify(cachedData.edited));

        setLoading(false);
        setError(null);
        setEditedDetails(freshCopy);

        console.log("Set editedDetails to cached data for property:", selectedPropertyId);
        return;
      }

      // If cache miss or expired, fetch from API
      console.log(`Fetching property ${selectedPropertyId} from API`);
      setLoading(true);
      setError(null);
      try {
        const details = await getBPOPDFInfo({ propertyId: selectedPropertyId });

        // Store in cache
        const cacheData: CachedPropertyData = {
          original: details,
          edited: JSON.parse(JSON.stringify(details)), // Deep copy for editing
          timestamp: Date.now(),
        };
        propertyCache.current.set(selectedPropertyId, cacheData);
        console.log(
          `Cached property ${selectedPropertyId}. Cache now has ${propertyCache.current.size} entries`
        );

        setEditedDetails(details);
      } catch (error) {
        console.error("Error fetching property details:", error);
        setError("Failed to load property details. Please try again later.");
      } finally {
        setLoading(false);
      }
    };

    loadPropertyDetails();
  }, [selectedPropertyId]);

  const handlePropertySelect = (propertyId: string) => {
    // Before switching, save current edited details to cache if they exist
    if (selectedPropertyId && editedDetails) {
      const existingCache = propertyCache.current.get(selectedPropertyId);
      if (existingCache) {
        existingCache.edited = JSON.parse(JSON.stringify(editedDetails));
        propertyCache.current.set(selectedPropertyId, existingCache);
        console.log(`Saved edits for property ${selectedPropertyId} before switching`);
      }
    }

    console.log(`Switching from property ${selectedPropertyId} to ${propertyId}`);
    setSelectedPropertyId(propertyId);
  };

  const handleInputChange = (section: keyof PropertyDetails, field: string, value: any) => {
    if (!editedDetails) return;

    const updatedDetails = {
      ...editedDetails,
      [section]: {
        ...editedDetails[section],
        [field]: value,
      },
    };

    setEditedDetails(updatedDetails);

    // Update cache with edited details
    const cachedData = propertyCache.current.get(selectedPropertyId);
    if (cachedData) {
      cachedData.edited = JSON.parse(JSON.stringify(updatedDetails));
      propertyCache.current.set(selectedPropertyId, cachedData);
    }
  };

  const confirmSign = async () => {
    console.log("confirmSign called");
    try {
      console.log("Signing property:", selectedPropertyId);
      console.log("Current order payload structure:", JSON.stringify(order.payload, null, 2));

      // Update the order with the new signed status while preserving existing payload structure
      const currentSignData = getSignData();
      
      // Create update payload with flat structure
      const updatePayload = {
        payload: {
          ...order.payload,
          sign: {
            ...currentSignData,
            [selectedPropertyId]: true,
          },
        },
      };

      console.log("Sending update payload structure:", JSON.stringify(updatePayload, null, 2));
      await updateOrder(order.id, updatePayload);

      // Update the local order state with the same structure
      const updatedProperties = localOrder.properties.map((prop) =>
        prop.propertyId === selectedPropertyId ? { ...prop, signed: true } : prop
      );

      // Update local order state preserving the flat structure
      const updatedOrder = {
        ...localOrder,
        properties: updatedProperties,
        payload: {
          ...localOrder.payload,
          sign: {
            ...currentSignData,
            [selectedPropertyId]: true,
          },
        },
      };

      setLocalOrder(updatedOrder);
      setIsConfirmingSign(false);

      // Show success notification
      const propertyAddress =
        localOrder.properties.find((p) => p.propertyId === selectedPropertyId)?.propertyAddress ||
        "Property";
      toast.success("Property signed successfully!", {
        description: `${propertyAddress} has been signed and updated.`,
        duration: 4000,
        icon: <CheckCircle className="h-4 w-4" />,
      });

      // Notify parent component about the signed property
      if (onPropertySigned) {
        onPropertySigned(order.id, selectedPropertyId);
      }
    } catch (error) {
      console.error("Error signing property:", error);
      setError("Failed to sign property. Please try again.");
      setIsConfirmingSign(false);

      // Show error notification
      toast.error("Failed to sign property", {
        description: "There was an error signing the property. Please try again.",
        duration: 4000,
      });
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-[95vw] w-full h-[95vh] flex flex-col">
        <DialogHeader>
          <DialogTitle>
            {order.customerName} - #{order.orderNumber} - ({signedCount} / {totalProperties})
          </DialogTitle>
        </DialogHeader>

        <div className="flex-1 flex gap-6 min-h-0">
          {/* Left Panel */}
          <div className="w-1/4 border-r pr-4">
            <ScrollArea className="h-full" type="always">
              {order.properties.map((property) => (
                <div
                  key={property.propertyId}
                  className={`p-4 cursor-pointer rounded-lg mb-2 ${
                    selectedPropertyId === property.propertyId ? "bg-primary/10" : "hover:bg-muted"
                  }`}
                  onClick={() => handlePropertySelect(property.propertyId)}
                >
                  <div className="font-medium">{property.propertyAddress}</div>
                  <div className="text-sm text-muted-foreground mt-1">
                    ID: {property.propertyId}
                  </div>
                  <div className="text-sm mt-1 flex items-center gap-1">
                    <span>Status:</span>
                    {isPropertySigned(property.propertyId) ? (
                      <span className="text-green-600 font-medium flex items-center gap-1">
                        <CheckCircle className="h-3 w-3" />
                        Signed
                      </span>
                    ) : (
                      <span className="text-orange-600 font-medium">Wait for sign</span>
                    )}
                  </div>
                </div>
              ))}
            </ScrollArea>
          </div>

          {/* Right Panel */}
          <div className="flex-1">
            <ScrollArea className="h-full" type="always">
              {loading ? (
                <div className="flex items-center justify-center h-full">
                  <Loader2 className="h-8 w-8 animate-spin" />
                </div>
              ) : error ? (
                <div className="flex items-center justify-center h-full">
                  <div className="text-destructive text-center">
                    <p>{error}</p>
                    <Button
                      variant="outline"
                      className="mt-4"
                      onClick={() => {
                        // Clear cache for this property to force a fresh API call
                        clearPropertyCache(selectedPropertyId);
                        setError(null);
                        // Force re-render by setting a temporary value and then back
                        const currentPropertyId = selectedPropertyId;
                        setSelectedPropertyId("");
                        setTimeout(() => setSelectedPropertyId(currentPropertyId), 0);
                      }}
                    >
                      Try Again
                    </Button>
                  </div>
                </div>
              ) : editedDetails ? (
                <Tabs defaultValue="property" className="w-full">
                  <TabsList>
                    <TabsTrigger value="property">Property Details</TabsTrigger>
                    <TabsTrigger value="active">Active Comparables</TabsTrigger>
                    <TabsTrigger value="closed">Closed Comparables</TabsTrigger>
                  </TabsList>

                  <TabsContent value="property" className="space-y-6">
                    {/* Basic Information */}
                    <div>
                      <h3 className="text-lg font-medium mb-4">Basic Information</h3>
                      <div className="grid grid-cols-3 gap-4">
                        <div>
                          <Label>Full Address</Label>
                          <Input value={editedDetails.fullAddress} readOnly className="bg-muted" />
                        </div>
                        <div>
                          <Label>Longitude</Label>
                          <Input value={editedDetails.longitude} readOnly className="bg-muted" />
                        </div>
                        <div>
                          <Label>Latitude</Label>
                          <Input value={editedDetails.latitude} readOnly className="bg-muted" />
                        </div>
                      </div>
                    </div>

                    <Separator />

                    {/* Order Information */}
                    <div>
                      <h3 className="text-lg font-medium mb-4">Order Information</h3>
                      <div className="grid grid-cols-3 gap-4">
                        {Object.entries(editedDetails.orderInformation).map(([key, value]) => (
                          <div key={key}>
                            <Label htmlFor={key}>{formatFieldName(key)}</Label>
                            <Input
                              id={key}
                              value={value || ""}
                              onChange={(e) =>
                                handleInputChange("orderInformation", key, e.target.value)
                              }
                            />
                          </div>
                        ))}
                      </div>
                    </div>

                    <Separator />

                    {/* Property Information */}
                    <div>
                      <h3 className="text-lg font-medium mb-4">Property Information</h3>
                      <div className="grid grid-cols-3 gap-4">
                        {Object.entries(editedDetails.propertyInformation).map(([key, value]) => (
                          <div key={key}>
                            <Label htmlFor={key}>{formatFieldName(key)}</Label>
                            <Input
                              id={key}
                              value={
                                isPrice(key, value) ? formatPrice(value as number) : value ?? ""
                              }
                              onChange={(e) => {
                                let newValue = e.target.value;
                                if (isPrice(key, value)) {
                                  // Remove currency formatting for price fields when saving
                                  newValue = e.target.value.replace(/[$,]/g, "");
                                }
                                handleInputChange("propertyInformation", key, newValue);
                              }}
                            />
                          </div>
                        ))}
                      </div>
                    </div>

                    <Separator />

                    {/* Neighborhood Information */}
                    <div>
                      <h3 className="text-lg font-medium mb-4">Neighborhood Information</h3>
                      <div className="grid grid-cols-3 gap-4">
                        {Object.entries(editedDetails.neighborhoodInformation).map(
                          ([key, value]) => (
                            <div key={key}>
                              <Label htmlFor={key}>{formatFieldName(key)}</Label>
                              <Input
                                id={key}
                                value={value || ""}
                                onChange={(e) =>
                                  handleInputChange("neighborhoodInformation", key, e.target.value)
                                }
                              />
                            </div>
                          )
                        )}
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="active">
                    <div className="space-y-4">
                      <h3 className="text-lg font-medium">Active Comparable Properties</h3>
                      {editedDetails.activeComparablePropertyInformationList.map(
                        (property, index) => (
                          <ComparablePropertyCard key={index} property={property} />
                        )
                      )}
                    </div>
                  </TabsContent>

                  <TabsContent value="closed">
                    <div className="space-y-4">
                      <h3 className="text-lg font-medium">Closed Comparable Properties</h3>
                      {editedDetails.closedComparablePropertyInformationList.map(
                        (property, index) => (
                          <ComparablePropertyCard key={index} property={property} />
                        )
                      )}
                    </div>
                  </TabsContent>
                </Tabs>
              ) : null}
            </ScrollArea>
          </div>
        </div>

        {/* Footer */}
        {!loading && (
          <div className="flex-shrink-0 flex justify-end items-center mt-4 pt-4 border-t gap-4">
            {isConfirmingSign ? (
              <>
                <span className="text-sm text-destructive mr-auto">
                  Are you sure all the property information is correct? This action cannot be
                  undone.
                </span>
                <Button variant="outline" onClick={() => setIsConfirmingSign(false)}>
                  Cancel
                </Button>
                <Button onClick={confirmSign}>Confirm Sign</Button>
              </>
            ) : (
              <Button
                onClick={() => setIsConfirmingSign(true)}
                disabled={isPropertySigned(selectedPropertyId)}
                className={
                  isPropertySigned(selectedPropertyId) ? "bg-green-600 hover:bg-green-600" : ""
                }
              >
                {isPropertySigned(selectedPropertyId) ? (
                  <span className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4" />
                    Already Signed
                  </span>
                ) : (
                  "Sign Property"
                )}
              </Button>
            )}
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default OrderPreviewModal;
