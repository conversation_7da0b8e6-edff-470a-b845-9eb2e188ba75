import { use<PERSON>tom } from "jotai";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Mail } from "lucide-react";

import {
  orderEmail<PERSON>tom,
  order<PERSON><PERSON><PERSON>tom,
} from "@/jotai/orders";

const OrderContactForm = () => {
  const [orderEmail, setOrderEmail] = useAtom(orderEmailAtom);
  const [orderPhone, setOrderPhone] = useAtom(orderPhoneAtom);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Mail className="h-5 w-5" />
          <span>Contact Information</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="order-email">Email Address *</Label>
            <Input
              id="order-email"
              type="email"
              placeholder="Enter contact email for this order..."
              value={orderEmail}
              onChange={(e) => setOrderEmail(e.target.value)}
              required
            />
            <p className="text-sm text-muted-foreground">
              Email for order notifications and updates
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="order-phone">Phone Number *</Label>
            <Input
              id="order-phone"
              type="tel"
              placeholder="Enter contact phone number..."
              value={orderPhone}
              onChange={(e) => setOrderPhone(e.target.value)}
              required
            />
            <p className="text-sm text-muted-foreground">
              Phone number for order-related communication
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default OrderContactForm;