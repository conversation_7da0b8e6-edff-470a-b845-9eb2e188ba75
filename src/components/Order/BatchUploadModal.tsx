import { useState, useCallback } from "react";
import { use<PERSON>tom } from "jotai";
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Upload,
  Download,
  FileText,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Loader2,
  Trash2,
  AlertCircle,
} from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { ScrollArea } from "@/components/ui/scroll-area";

import { propertyAddressesAtom, ValidatedAddress } from "@/jotai/orders";
import { postValidateAddress } from "@/services/data";

interface AddressRecord {
  address: string;
  city: string;
  state: string;
  zip: string;
  fullAddress: string;
  isValid: boolean | null;
  isValidating: boolean;
  rowIndex: number;
  propertyId?: string;
  apiReturnedAddress?: string;
  hasDiscrepancy: boolean;
  discrepancyResolved: boolean;
  valuationData?: {
    estimatedValue: number | null;
    confidenceScore: number | null;
    statusMessage: string;
  };
}

interface BatchUploadModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const ValuationInfo = ({ valuationData }: { valuationData?: AddressRecord['valuationData'] }) => {
  if (!valuationData) return null;

  const formatCurrency = (value: number | null) => {
    if (value === null) return 'N/A';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      maximumFractionDigits: 0
    }).format(value);
  };

  return (
    <div className="mt-2 p-3 bg-blue-50 border border-blue-200 rounded-lg">
      <div className="flex items-start space-x-2">
        <div className="flex-1">
          <p className="text-sm font-medium text-blue-800">Property Valuation Data</p>
          <div className="mt-2 grid grid-cols-2 gap-4 text-xs">
            <div>
              <p className="font-medium text-gray-700">Estimated Value:</p>
              <p className="text-gray-600">
                {formatCurrency(valuationData.estimatedValue)}
              </p>
            </div>
            <div>
              <p className="font-medium text-gray-700">Confidence Score:</p>
              <p className="text-gray-600">
                {valuationData.confidenceScore !== null ? `${valuationData.confidenceScore}%` : 'N/A'}
              </p>
            </div>
          </div>
          <div className="mt-2">
            <p className="text-xs text-gray-500 italic">{valuationData.statusMessage}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

const AddressDiscrepancy = ({
  address,
  onResolve,
}: {
  address: AddressRecord;
  onResolve: (useApiAddress: boolean) => void;
}) => {
  if (!address.hasDiscrepancy || !address.apiReturnedAddress || address.discrepancyResolved) {
    return null;
  }

  // Split addresses into components for comparison
  const original = {
    street: address.address,
    city: address.city,
    state: address.state,
    zip: address.zip
  };

  const suggested = {
    street: address.apiReturnedAddress.split(',')[0],
    city: address.apiReturnedAddress.split(',')[1]?.trim() || '',
    state: address.apiReturnedAddress.split(',')[2]?.trim()?.split(' ')[0] || '',
    zip: address.apiReturnedAddress.split(',')[2]?.trim()?.split(' ')[1] || ''
  };

  return (
    <>
      <div className="mt-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
        <div className="flex items-start space-x-2">
          <AlertCircle className="h-4 w-4 text-yellow-600 mt-0.5" />
          <div className="flex-1">
            <p className="text-sm font-medium text-yellow-800">Address Discrepancy Found</p>
            <div className="mt-2 space-y-2">
              <div className="grid grid-cols-2 gap-4 text-xs">
                <div>
                  <p className="font-medium text-gray-700">Original Address:</p>
                  <div className="mt-1 space-y-0.5 text-gray-600">
                    <p>{original.street}</p>
                    <p>{original.city}, {original.state} {original.zip}</p>
                  </div>
                </div>
                <div>
                  <p className="font-medium text-gray-700">Suggested Address:</p>
                  <div className="mt-1 space-y-0.5 text-gray-600">
                    <p>{suggested.street}</p>
                    <p>{suggested.city}, {suggested.state} {suggested.zip}</p>
                  </div>
                </div>
              </div>
              <p className="text-xs text-gray-500 italic">
                The suggested address was found in our property database. Please verify which address is correct.
              </p>
            </div>
            <div className="mt-3 flex space-x-2">
              <Button
                size="sm"
                variant="outline"
                className="h-7 bg-white hover:bg-green-50 hover:border-green-200 hover:text-green-700"
                onClick={() => onResolve(true)}
              >
                <CheckCircle className="h-3 w-3 mr-1" />
                <span className="text-xs">Use Suggested</span>
              </Button>
              <Button
                size="sm"
                variant="outline"
                className="h-7 bg-white hover:bg-red-50 hover:border-red-200 hover:text-red-700"
                onClick={() => onResolve(false)}
              >
                <XCircle className="h-3 w-3 mr-1" />
                <span className="text-xs">Remove the address</span>
              </Button>
            </div>
          </div>
        </div>
      </div>
      {address.valuationData && <ValuationInfo valuationData={address.valuationData} />}
    </>
  );
};

const BatchUploadModal = ({ isOpen, onClose }: BatchUploadModalProps) => {
  const [, setPropertyAddresses] = useAtom(propertyAddressesAtom);

  const [dragActive, setDragActive] = useState(false);
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [addresses, setAddresses] = useState<AddressRecord[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Real address validation function using DataTree API
  const validateAddress = async (address: AddressRecord): Promise<{
    isValid: boolean;
    apiAddress?: string;
    propertyId?: string;
    valuationData?: {
      estimatedValue: number | null;
      confidenceScore: number | null;
      statusMessage: string;
    };
  }> => {
    try {
      // Construct full address string
      const fullAddress = `${address.address}, ${address.city}, ${address.state} ${address.zip}`;
      
      // Call the DataTree API through our service
      const result = await postValidateAddress(fullAddress);
      
      // Check if we have a valid property in the response
      if (result.isValid && result.propertyDetails) {
        const apiProperty = result.propertyDetails;
        
        // Construct the full API address
        const apiFullAddress = `${apiProperty.Address}, ${apiProperty.City}, ${apiProperty.State} ${apiProperty.Zip}`;
        
        // Compare addresses (ignoring case and basic formatting)
        const normalizedInput = fullAddress.toLowerCase().replace(/\s+/g, ' ').trim();
        const normalizedApi = apiFullAddress.toLowerCase().replace(/\s+/g, ' ').trim();
        
        // Extract valuation data if available
        const valuationData = result.valuationSummary ? {
          estimatedValue: result.valuationSummary.EstimatedValue,
          confidenceScore: result.valuationSummary.ConfidenceScore,
          statusMessage: result.valuationSummary.AVMStatusMessage
        } : undefined;

        if (normalizedInput !== normalizedApi) {
          return {
            isValid: true,
            apiAddress: apiFullAddress,
            propertyId: apiProperty.PropertyId.toString(),
            valuationData
          };
        }
        
        // Addresses match
        return {
          isValid: true,
          propertyId: apiProperty.PropertyId.toString(),
          valuationData
        };
      }
      
      // No valid property found
      return { isValid: false };
    } catch (error) {
      console.error("Address validation failed:", error);
      return { isValid: false };
    }
  };

  // Download template function
  const downloadTemplate = () => {
    const csvContent =
      "address,city,state,zip\n123 Main Street,Anytown,CA,12345\n456 Oak Avenue,Springfield,NY,54321\n789 Pine Road,Madison,TX,98765";
    const blob = new Blob([csvContent], { type: "text/csv" });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = "address_template.csv";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  };

  // File processing function
  const processFile = async (file: File) => {
    setIsProcessing(true);
    setError(null);

    try {
      const text = await file.text();
      let data: any[] = [];

      if (file.name.endsWith(".csv")) {
        // Parse CSV
        const lines = text.split("\n").filter((line) => line.trim());
        if (lines.length < 2) {
          throw new Error("CSV file must have at least a header and one data row");
        }

        const headers = lines[0].split(",").map((h) => h.trim().toLowerCase());
        const addressIndex = headers.findIndex((h) => h.includes("address"));
        const cityIndex = headers.findIndex((h) => h.includes("city"));
        const stateIndex = headers.findIndex((h) => h.includes("state"));
        const zipIndex = headers.findIndex((h) => h.includes("zip"));

        if (addressIndex === -1 || cityIndex === -1 || stateIndex === -1 || zipIndex === -1) {
          throw new Error("CSV must contain columns: address, city, state, zip");
        }

        data = lines.slice(1).map((line, index) => {
          const values = line.split(",").map((v) => v.trim().replace(/"/g, ""));
          return {
            address: values[addressIndex] || "",
            city: values[cityIndex] || "",
            state: values[stateIndex] || "",
            zip: values[zipIndex] || "",
            rowIndex: index + 2, // +2 for header and 0-based index
          };
        });
      } else if (file.name.endsWith(".xlsx")) {
        // For XLSX, we'd need SheetJS library
        // Mock XLSX parsing for now
        throw new Error("XLSX support will be implemented with SheetJS library");
      } else {
        throw new Error("Only CSV and XLSX files are supported");
      }

      // Filter out empty rows
      data = data.filter((row) => row.address && row.city && row.state && row.zip);

      if (data.length === 0) {
        throw new Error("No valid address records found in the file");
      }

      // Create address records
      const addressRecords: AddressRecord[] = data.map((row) => ({
        ...row,
        fullAddress: `${row.address}, ${row.city}, ${row.state} ${row.zip}`,
        isValid: null,
        isValidating: false,
        apiReturnedAddress: undefined,
        hasDiscrepancy: false,
        discrepancyResolved: false,
      }));

      setAddresses(addressRecords);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to process file");
    } finally {
      setIsProcessing(false);
    }
  };

  // Drag and drop handlers
  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    const files = Array.from(e.dataTransfer.files);
    const file = files[0];

    if (file && (file.name.endsWith(".csv") || file.name.endsWith(".xlsx"))) {
      setUploadedFile(file);
      processFile(file);
    } else {
      setError("Please upload a CSV or XLSX file");
    }
  }, []);

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setUploadedFile(file);
      processFile(file);
    }
  };

  // Validate all addresses
  const validateAllAddresses = async () => {
    const updatedAddresses = [...addresses];

    // Set all to validating
    updatedAddresses.forEach((addr) => {
      addr.isValidating = true;
      addr.isValid = null;
    });
    setAddresses([...updatedAddresses]);

    // Helper function to sleep
    const sleep = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

    // Validate each address with delay
    for (let i = 0; i < updatedAddresses.length; i++) {
      try {
        const result = await validateAddress(updatedAddresses[i]);
        
        if (result.isValid && result.apiAddress) {
          // Address discrepancy found
          updatedAddresses[i].hasDiscrepancy = true;
          updatedAddresses[i].apiReturnedAddress = result.apiAddress;
          updatedAddresses[i].propertyId = result.propertyId;
          updatedAddresses[i].valuationData = result.valuationData;
          updatedAddresses[i].isValid = null; // Wait for user decision
        } else {
          // No discrepancy
          updatedAddresses[i].isValid = result.isValid;
          updatedAddresses[i].propertyId = result.propertyId;
          updatedAddresses[i].valuationData = result.valuationData;
        }
      } catch {
        updatedAddresses[i].isValid = false;
      } finally {
        updatedAddresses[i].isValidating = false;
        setAddresses([...updatedAddresses]);
      }
      
      // Add 3 second delay between validations
      if (i < updatedAddresses.length - 1) { // Don't delay after the last item
        await sleep(3000);
      }
    }
  };

  // Add resolve discrepancy function
  const resolveDiscrepancy = (index: number, useApiAddress: boolean) => {
    const updatedAddresses = [...addresses];
    const address = updatedAddresses[index];
    
    if (useApiAddress && address.apiReturnedAddress) {
      address.fullAddress = address.apiReturnedAddress;
    }
    
    address.isValid = useApiAddress; // If using API address, mark as valid; if not, mark as invalid
    address.discrepancyResolved = true;
    address.hasDiscrepancy = false;
    
    setAddresses(updatedAddresses);
  };

  // Remove address
  const removeAddress = (index: number) => {
    const newAddresses = addresses.filter((_, i) => i !== index);
    setAddresses(newAddresses);
  };

  // Add valid addresses to order
  const addValidAddresses = () => {
    const validAddresses = addresses
      .filter((addr) => addr.isValid === true)
      .map((addr) => ({
        fullAddress: addr.fullAddress,
        propertyId: addr.propertyId,
        valuationData: addr.valuationData
      } as ValidatedAddress));

    if (validAddresses.length > 0) {
      setPropertyAddresses((prev) => [...prev, ...validAddresses]);
      onClose();
      resetModal();
    }
  };

  // Reset modal state
  const resetModal = () => {
    setUploadedFile(null);
    setAddresses([]);
    setError(null);
    setIsProcessing(false);
    setDragActive(false);
  };

  const handleClose = () => {
    onClose();
    resetModal();
  };

  const validCount = addresses.filter((addr) => addr.isValid === true).length;
  const invalidCount = addresses.filter((addr) => addr.isValid === false).length;
  const pendingCount = addresses.filter(
    (addr) => addr.isValid === null && !addr.isValidating
  ).length;
  const validatingCount = addresses.filter((addr) => addr.isValidating).length;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle className="text-md">Batch Upload Addresses</DialogTitle>
          <DialogDescription>
            <span className="text-sm">
              Upload a CSV or XLSX file containing property addresses for batch processing
            </span>
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-hidden">
          {!uploadedFile ? (
            <div className="space-y-4">
              {/* Download Template */}
              <div className="flex items-center justify-between p-4 border rounded-lg bg-blue-50 border-blue-200">
                <div>
                  <h3 className="font-medium text-blue-900 text-sm">Download Template</h3>
                  <p className="text-xs text-blue-700">
                    Get the CSV template with required columns
                  </p>
                </div>
                <Button onClick={downloadTemplate} variant="outline" className="space-x-2">
                  <Download className="h-3 w-3" />
                  <span className="text-sm">Download Template</span>
                </Button>
              </div>

              {/* Upload Area */}
              <div
                className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                  dragActive
                    ? "border-primary bg-primary/5"
                    : "border-gray-300 hover:border-gray-400"
                }`}
                onDragEnter={handleDrag}
                onDragLeave={handleDrag}
                onDragOver={handleDrag}
                onDrop={handleDrop}
              >
                <Upload className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                <p className="text-md font-medium mb-2">
                  {dragActive ? "Drop your file here" : "Upload Address File"}
                </p>
                <p className="text-gray-500 mb-4 text-sm">
                  Drag and drop your CSV or XLSX file, or click to browse
                </p>
                <input
                  type="file"
                  accept=".csv,.xlsx"
                  onChange={handleFileSelect}
                  className="hidden"
                  id="file-upload"
                />
                <label htmlFor="file-upload">
                  <Button variant="outline" className="cursor-pointer" asChild>
                    <span>Browse Files</span>
                  </Button>
                </label>
                <p className="text-xs text-gray-400 mt-2">
                  Supported formats: CSV, XLSX (Max 10MB)
                </p>
              </div>

              {error && (
                <Alert variant="destructive">
                  <AlertTriangle className="h-3 w-3" />
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}
            </div>
          ) : (
            <div className="space-y-4 h-full flex flex-col">
              {/* File Info */}
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center space-x-2">
                  <FileText className="h-5 w-5 text-blue-600" />
                  <div>
                    <p className="font-medium text-sm">{uploadedFile.name}</p>
                    <p className="text-xs text-gray-500">{addresses.length} addresses found</p>
                  </div>
                </div>
                <div className="flex space-x-2">
                  {addresses.length > 0 && (
                    <Button
                      onClick={validateAllAddresses}
                      disabled={isProcessing || validatingCount > 0}
                      className="space-x-2"
                    >
                      {validatingCount > 0 ? (
                        <Loader2 className="h-3 w-3 animate-spin" />
                      ) : (
                        <CheckCircle className="h-3 w-3" />
                      )}
                      <span className="text-sm">Validate All</span>
                    </Button>
                  )}
                  <Button variant="outline" onClick={() => setUploadedFile(null)}>
                    <span className="text-sm">Upload Different File</span>
                  </Button>
                </div>
              </div>

              {/* Status Summary */}
              {addresses.length > 0 && (
                <div className="flex space-x-4">
                  <Badge variant="default" className="bg-green-100 text-green-800">
                    Valid: {validCount}
                  </Badge>
                  <Badge variant="destructive">Invalid: {invalidCount}</Badge>
                  {pendingCount > 0 && <Badge variant="secondary">Pending: {pendingCount}</Badge>}
                  {validatingCount > 0 && (
                    <Badge variant="outline">Validating: {validatingCount}</Badge>
                  )}
                </div>
              )}

              {/* Address List */}
              <div className="flex-1 min-h-0">
                <ScrollArea className="h-[400px] border rounded-lg">
                  <div className="p-4 space-y-4">
                    {addresses.map((address, index) => (
                      <div
                        key={`${address.fullAddress}-${index}`}
                        className="border rounded-lg p-4 relative"
                      >
                        {/* Remove button */}
                        <button
                          onClick={() => removeAddress(index)}
                          className="absolute top-2 right-2 text-gray-400 hover:text-red-600"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>

                        {/* Address details */}
                        <div className="pr-6">
                          <div className="flex items-center space-x-2 mb-2">
                            <p className="font-medium text-sm">
                              {address.address}, {address.city}, {address.state} {address.zip}
                            </p>
                            {address.isValidating ? (
                              <Badge variant="outline" className="bg-gray-100">
                                <Loader2 className="h-3 w-3 animate-spin mr-1" />
                                Validating
                              </Badge>
                            ) : address.isValid === true ? (
                              <Badge className="bg-green-100 text-green-800 hover:bg-green-100">
                                <CheckCircle className="h-3 w-3 mr-1" />
                                Valid
                              </Badge>
                            ) : address.isValid === false ? (
                              <Badge variant="destructive">
                                <XCircle className="h-3 w-3 mr-1" />
                                Invalid
                              </Badge>
                            ) : null}
                          </div>

                          {/* Show discrepancy UI if needed */}
                          {address.hasDiscrepancy && (
                            <AddressDiscrepancy
                              address={address}
                              onResolve={(useApiAddress) => resolveDiscrepancy(index, useApiAddress)}
                            />
                          )}

                          {/* Show valuation data for valid addresses without discrepancy */}
                          {!address.hasDiscrepancy && address.isValid && address.valuationData && (
                            <ValuationInfo valuationData={address.valuationData} />
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            <span className="text-sm">Cancel</span>
          </Button>
          {addresses.length > 0 && validCount > 0 && (
            <Button onClick={addValidAddresses} className="space-x-2">
              <CheckCircle className="h-3 w-3" />
              <span className="text-sm">Add {validCount} Valid Addresses</span>
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default BatchUploadModal;