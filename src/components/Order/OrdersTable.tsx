import { useState, useMemo, useEffect } from 'react';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuCheckboxItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { 
  FileText, 
  Download, 
  ChevronUp, 
  ChevronDown,
  Filter,
  Loader2,
  Eye,
  Search,
  CheckCircle
} from 'lucide-react';
import { OrderStatus } from '@/jotai/types';
import { getAllOrders, APIOrder } from '@/services/data';
import ValuationDetailsModal from './ValuationDetailsModal';
import OrderPreviewModal from './OrderPreviewModal';
import { ConfidenceScore } from "@/components/ui/confidence-score";

type SortField = 'id' | 'customerName' | 'orderNumber' | 'status' | 'totalPrice';
type SortDirection = 'asc' | 'desc' | null;

interface OrdersTableProps {
  onReviewClick?: (order: APIOrder) => void;
  onDownloadClick?: (order: APIOrder) => void;
}

const OrdersTable: React.FC<OrdersTableProps> = ({
  onReviewClick,
  onDownloadClick,
}) => {
  // State
  const [orders, setOrders] = useState<APIOrder[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sortField, setSortField] = useState<SortField>('id');
  const [sortDirection, setSortDirection] = useState<SortDirection>('desc');
  const [customerFilter, setCustomerFilter] = useState('');
  const [selectedStatuses, setSelectedStatuses] = useState<string[]>([]);
  const [priceRange] = useState({ min: '', max: '' });
  const [selectedOrder, setSelectedOrder] = useState<APIOrder | null>(null);
  const [isValuationModalOpen, setIsValuationModalOpen] = useState(false);
  const [selectedOrderForPreview, setSelectedOrderForPreview] = useState<APIOrder | null>(null);
  const [isPreviewModalOpen, setIsPreviewModalOpen] = useState(false);

  // Fetch orders
  useEffect(() => {
    const fetchOrders = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await getAllOrders();
        setOrders(response);
      } catch (err) {
        setError('Failed to fetch orders. Please try again later.');
        console.error('Error fetching orders:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchOrders();
  }, []);

  // Sorting logic
  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : sortDirection === 'desc' ? null : 'asc');
      if (sortDirection === null) {
        setSortField(field);
      }
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Status badge styling
  const getStatusBadgeVariant = (status: string | undefined) => {
    if (!status) return 'bg-gray-100 text-gray-800 border-gray-200';
    
    switch (status.toLowerCase()) {
      case 'draft':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'submitted':
        return 'bg-blue-50 text-blue-800 border-blue-200';
      case 'in progress':
        return 'bg-yellow-50 text-yellow-800 border-yellow-200';
      case 'in review':
        return 'bg-purple-50 text-purple-800 border-purple-200';
      case 'done':
        return 'bg-green-50 text-green-800 border-green-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  // Filtered and sorted orders
  const displayOrders = useMemo(() => {
    let filtered = [...orders];

    // Apply customer filter
    if (customerFilter) {
      filtered = filtered.filter(order => 
        order.customerName.toLowerCase().includes(customerFilter.toLowerCase())
      );
    }

    // Apply status filter
    if (selectedStatuses.length > 0) {
      filtered = filtered.filter(order => selectedStatuses.includes(order.status));
    }

    // Apply price range filter
    if (priceRange.min || priceRange.max) {
      filtered = filtered.filter(order => {
        const price = order.totalPrice;
        const min = priceRange.min ? parseFloat(priceRange.min) : -Infinity;
        const max = priceRange.max ? parseFloat(priceRange.max) : Infinity;
        return price >= min && price <= max;
      });
    }

    // Apply sorting
    if (sortField && sortDirection) {
      filtered.sort((a, b) => {
        let aValue = a[sortField];
        let bValue = b[sortField];

        // Convert to lowercase only if string
        if (typeof aValue === 'string' && typeof bValue === 'string') {
          aValue = aValue.toLowerCase();
          bValue = bValue.toLowerCase();
        }

        if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
        if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
        return 0;
      });
    }

    return filtered;
  }, [orders, sortField, sortDirection, customerFilter, selectedStatuses, priceRange]);

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(price);
  };

  const getSortIcon = (field: SortField) => {
    if (sortField !== field) return null;
    return sortDirection === 'asc' ? (
      <ChevronUp className="h-4 w-4 ml-1 inline" />
    ) : sortDirection === 'desc' ? (
      <ChevronDown className="h-4 w-4 ml-1 inline" />
    ) : null;
  };

  const handleValuationClick = (order: APIOrder) => {
    setSelectedOrder(order);
    setIsValuationModalOpen(true);
  };

  const handlePreviewClick = (order: APIOrder) => {
    setSelectedOrderForPreview(order);
    setIsPreviewModalOpen(true);
  };

  // Helper function to get sign data from payload.sign (flat structure)
  const getOrderSignData = (order: APIOrder) => {
    return order.payload?.sign || {};
  };

  const handlePropertySigned = (orderId: number, propertyId: string) => {
    setOrders(prevOrders => 
      prevOrders.map(order => {
        if (order.id === orderId) {
          const currentSignData = getOrderSignData(order);
          
          return {
            ...order,
            properties: order.properties.map(prop =>
              prop.propertyId === propertyId
                ? { ...prop, signed: true }
                : prop
            ),
            payload: {
              ...order.payload,
              sign: {
                ...currentSignData,
                [propertyId]: true
              }
            }
          };
        }
        return order;
      })
    );
  };

  const renderValuationCell = (order: APIOrder) => {
    console.log("order", order);
    const valuation = order.payload?.propertyValuations?.find(
      (v) => v.propertyId === order.properties[0]?.propertyId
    )?.valuationData;

    if (order.properties.length > 1) {
      return (
        <Button
          variant="outline"
          size="sm"
          onClick={() => handleValuationClick(order)}
        >
          <Eye className="h-4 w-4 mr-1" />
          View Details
        </Button>
      );
    }

    if (!valuation) {
      return <span className="text-gray-500">No data</span>;
    }

    if (valuation.estimatedValue === null) {
      return <span className="text-gray-500 italic">{valuation.statusMessage}</span>;
    }

    return (
      <div className="text-sm">
        {valuation.estimatedValue !== null && (
          <div className="flex items-center gap-3">
            <div className="font-medium">{formatPrice(valuation.estimatedValue)}</div>
            {valuation.confidenceScore !== null && (
              <ConfidenceScore score={valuation.confidenceScore} size="sm" />
            )}
          </div>
        )}
      </div>
    );
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-6">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span className="ml-2">Loading orders...</span>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="text-red-500 py-6">
          {error}
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Orders</CardTitle>
        <CardDescription>
          Manage and track all orders
        </CardDescription>
      </CardHeader>
      <CardContent>
        {/* Filters */}
        <div className="space-y-4 mb-4">
          <div className="flex flex-wrap gap-4">
            <div className="flex-1 min-w-[200px]">
              <Input
                placeholder="Filter by customer name..."
                value={customerFilter}
                onChange={(e) => setCustomerFilter(e.target.value)}
              />
            </div>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline">
                  <Filter className="h-4 w-4 mr-2" />
                  Status Filter
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                {(['draft', 'submitted', 'in progress', 'in review', 'done'] as OrderStatus[]).map((status) => (
                  <DropdownMenuCheckboxItem
                    key={status}
                    checked={selectedStatuses.includes(status)}
                    onCheckedChange={(checked) => {
                      setSelectedStatuses(prev =>
                        checked
                          ? [...prev, status]
                          : prev.filter(s => s !== status)
                      );
                    }}
                  >
                    {status.charAt(0).toUpperCase() + status.slice(1)}
                  </DropdownMenuCheckboxItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
          {/* <div className="flex flex-wrap gap-4">
            <div className="flex gap-2 items-center">
              <Input
                type="number"
                placeholder="Min price"
                value={priceRange.min}
                onChange={(e) => setPriceRange(prev => ({ ...prev, min: e.target.value }))}
                className="w-[120px]"
              />
              <span>to</span>
              <Input
                type="number"
                placeholder="Max price"
                value={priceRange.max}
                onChange={(e) => setPriceRange(prev => ({ ...prev, max: e.target.value }))}
                className="w-[120px]"
              />
            </div>
          </div> */}
        </div>

        {/* Table */}
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead 
                  className="cursor-pointer"
                  onClick={() => handleSort('id')}
                >
                  <div className="flex items-center">
                    ID {getSortIcon('id')}
                  </div>
                </TableHead>
                <TableHead 
                  className="cursor-pointer"
                  onClick={() => handleSort('customerName')}
                >
                  <div className="flex items-center">
                    Customer Name {getSortIcon('customerName')}
                  </div>
                </TableHead>
                <TableHead 
                  className="cursor-pointer"
                  onClick={() => handleSort('orderNumber')}
                >
                  <div className="flex items-center">
                    Order Number {getSortIcon('orderNumber')}
                  </div>
                </TableHead>
                <TableHead>
                  Properties
                </TableHead>
                <TableHead>
                  Valuation
                </TableHead>
                <TableHead 
                  className="cursor-pointer"
                  onClick={() => handleSort('totalPrice')}
                >
                  <div className="flex items-center">
                    Total Price {getSortIcon('totalPrice')}
                  </div>
                </TableHead>
                <TableHead 
                  className="cursor-pointer"
                  onClick={() => handleSort('status')}
                >
                  <div className="flex items-center">
                    Status {getSortIcon('status')}
                  </div>
                </TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {displayOrders.map((order) => (
                <TableRow key={order.id}>
                  <TableCell>{order.id}</TableCell>
                  <TableCell>{order.customerName}</TableCell>
                  <TableCell>{order.orderNumber}</TableCell>
                  <TableCell>
                    <div className="max-h-24 overflow-y-auto space-y-1">
                      {order.properties && order.properties.length > 1 ? (
                        <div className="space-y-1">
                          <div className="text-sm font-medium">
                            {order.properties.length} Properties
                          </div>
                          <div className="text-xs text-muted-foreground">
                            Signed: {order.properties.filter(p => {
                              const signData = getOrderSignData(order);
                              return signData[p.propertyId] === true || p.signed;
                            }).length} / {order.properties.length}
                          </div>
                        </div>
                      ) : order.properties && order.properties.length === 1 ? (
                        <div className="space-y-1">
                          <div className="text-sm">
                            {order.properties[0].propertyAddress}
                          </div>
                          <div className="text-xs text-muted-foreground flex items-center gap-1">
                            {(() => {
                              const signData = getOrderSignData(order);
                              return (signData[order.properties[0].propertyId] === true || order.properties[0].signed);
                            })() ? (
                              <>
                                <CheckCircle className="h-3 w-3 text-green-600" />
                                <span className="text-green-600">Signed</span>
                              </>
                            ) : (
                              <span className="text-orange-600">Pending signature</span>
                            )}
                          </div>
                        </div>
                      ) : (
                        <div className="text-sm text-gray-500">
                          No properties
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    {renderValuationCell(order)}
                  </TableCell>
                  <TableCell>{formatPrice(order.totalPrice)}</TableCell>
                  <TableCell>
                    <Badge 
                      variant="outline" 
                      className={getStatusBadgeVariant(order.status)}
                    >
                      {order.status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex gap-2">
                      {order.status === 'in progress' && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handlePreviewClick(order)}
                        >
                          <Search className="h-4 w-4 mr-1" />
                          Preview
                        </Button>
                      )}
                      {onReviewClick && order.status === 'in review' && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => onReviewClick(order)}
                        >
                          <FileText className="h-4 w-4 mr-1" />
                          Review
                        </Button>
                      )}
                      {onDownloadClick && order.status === 'done' && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => onDownloadClick(order)}
                        >
                          <Download className="h-4 w-4 mr-1" />
                          Download
                        </Button>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </CardContent>
      {selectedOrder && (
        <ValuationDetailsModal
          order={selectedOrder}
          isOpen={isValuationModalOpen}
          onClose={() => {
            setIsValuationModalOpen(false);
            setSelectedOrder(null);
          }}
        />
      )}
      {selectedOrderForPreview && (
        <OrderPreviewModal
          order={selectedOrderForPreview}
          isOpen={isPreviewModalOpen}
          onClose={() => {
            setIsPreviewModalOpen(false);
            setSelectedOrderForPreview(null);
          }}
          onPropertySigned={handlePropertySigned}
        />
      )}
    </Card>
  );
};

export default OrdersTable; 