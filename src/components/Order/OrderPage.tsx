import { use<PERSON>tom } from "jotai";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";

import CustomerSearch from "@/components/Order/CustomerSearch";
import OrdersTable from "@/components/Order/OrdersTable";
import OrderConfiguration from "@/components/Order/OrderConfiguration";
import OrderReview from "@/components/Order/OrderReview";
import { 
  orderStepAtom, 
  selectedOrderCustomerAtom,
  resetOrderAtom 
} from "@/jotai/orders";
import { selectedCustomerAtom } from "@/jotai/customers";
import CustomerDetails from "../Customer/Details";
import CreateCustomerForm from "../Customer/CreateForm";
import { APIOrder } from "@/services/data";

const OrderPage = () => {
  const [orderStep] = useAtom(orderStepAtom);
  const [selectedOrderCustomer] = useAtom(selectedOrderCustomerAtom);
  const [, setSelectedCustomer] = useAtom(selectedCustomerAtom);
  const [, resetOrder] = useAtom(resetOrderAtom);
  const [, setOrderStep] = useAtom(orderStepAtom);

  const handleBackToSearch = () => {
    resetOrder();
    setSelectedCustomer(null); // Clear the customer details atom
  };

  const handleReviewClick = (order: APIOrder) => {
    // TODO: Implement review functionality
    console.log('Review order:', order);
  };

  const handleDownloadClick = (order: APIOrder) => {
    // TODO: Implement download functionality
    console.log('Download order:', order);
  };

  const renderOrderStep = () => {
    switch (orderStep) {
      case 1:
        return (
          <div className="space-y-6">
            <CustomerSearch />
            <OrdersTable 
              onReviewClick={handleReviewClick}
              onDownloadClick={handleDownloadClick}
            />
          </div>
        );
      
      case 2:
        // Sync the selected customer with the customer details atom
        if (selectedOrderCustomer) {
          setSelectedCustomer(selectedOrderCustomer);
        }
        
        return (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-2xl font-bold tracking-tight">Customer Selected</h2>
                <p className="text-muted-foreground">
                  Review customer details and proceed to order configuration
                </p>
              </div>
              <div className="flex items-center space-x-2">
                <Button 
                  variant="outline" 
                  onClick={handleBackToSearch}
                  className="space-x-2"
                >
                  <ArrowLeft className="h-4 w-4" />
                  <span>Back to Search</span>
                </Button>
                <Button 
                  onClick={() => setOrderStep(3)}
                  className="space-x-2"
                >
                  <span>Continue to Order</span>
                </Button>
              </div>
            </div>

            {selectedOrderCustomer && <CustomerDetails />}
          </div>
        );
      
      case 3:
        return <OrderConfiguration />;
      
      case 4:
        return <OrderReview />;
      
      default:
        return <CustomerSearch />;
    }
  };

  return (
    <div className="container mx-auto py-8 px-4 max-w-6xl">
      {renderOrderStep()}
      <CreateCustomerForm />
    </div>
  );
};

export default OrderPage;