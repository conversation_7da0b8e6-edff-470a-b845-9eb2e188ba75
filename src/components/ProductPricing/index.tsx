import React, { useState, use<PERSON>emo, useEffect } from "react";
import { use<PERSON>tom } from "jotai";
import { user<PERSON><PERSON> } from "@/jotai/user";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { DollarSign, AlertCircle, CheckCircle, Loader2, Lock, Eye } from "lucide-react";
import { getAllPricingStructures, postUpdatePricingStructure } from "@/services/data";

// 1. TypeScript Interface for Product Pricing Data
interface Product {
  id: number;
  name: string;
  productCode: number;
  standardPrice: number;
  lastUpdated: string;
}

// Mock Data
// const mockProducts: Product[] = [
//   { id: "PROD-001", name: "Basic BPO Service", standardPrice: 199.99, lastUpdated: "07/21/2023 10:30 AM" },
//   { id: "PROD-002", name: "Advanced BPO Service", standardPrice: 499.50, lastUpdated: "07/20/2023 03:45 PM" },
//   { id: "PROD-003", name: "Premium BPO Package", standardPrice: 999.00, lastUpdated: "07/22/2023 09:00 AM" },
//   { id: "PROD-004", name: "Custom BPO Solution", standardPrice: 1500.00, lastUpdated: "07/19/2023 11:00 AM" },
// ];

const ProductPricing: React.FC = () => {
  const [user] = useAtom(userAtom);
  
  // Check if user has admin privileges by looking for 'bpo-admin' in userGroup
  const isAdmin = useMemo(() => {
    if (!user?.userGroup) return false;
    
    // Handle both string array and string cases
    if (Array.isArray(user.userGroup)) {
      return user.userGroup.includes('bpo-admin');
    }
    
    return user.userGroup === 'bpo-admin';
  }, [user?.userGroup]);

  const [products, setProducts] = useState<Product[]>([]);
  const [originalProducts, setOriginalProducts] = useState<Product[]>([]);
  const [priceInputs, setPriceInputs] = useState<Record<string, string>>({});
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [showSuccessAlert, setShowSuccessAlert] = useState(false);
  const [showErrorAlert, setShowErrorAlert] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [lastUpdateCount, setLastUpdateCount] = useState(0);
  
  useEffect(() => {
    const fetchPricing = async () => {
      try {
        const pricingData = await getAllPricingStructures();
        // Adapt the fetched data to match the component's Product interface
        const adaptedProducts: Product[] = pricingData.map((p: any) => ({
          id: p.id,
          name: p.name,
          productCode: p.productCode,
          standardPrice: parseFloat(p.standardPricing) || 0,
          lastUpdated: new Date(p.modifiedAt).toLocaleString('en-US', { 
            month: '2-digit', 
            day: '2-digit', 
            year: 'numeric', 
            hour: '2-digit', 
            minute: '2-digit', 
            hour12: true 
          }),
        }));

        setProducts(adaptedProducts);
        setOriginalProducts(JSON.parse(JSON.stringify(adaptedProducts)));

        const initialInputs = adaptedProducts.reduce((acc, p) => {
          acc[p.id] = p.standardPrice.toFixed(2);
          return acc;
        }, {} as Record<string, string>);
        setPriceInputs(initialInputs);

      } catch (error) {
        console.error("Failed to fetch pricing structures:", error);
        // Handle error state in UI if necessary
      }
    };
    
    fetchPricing();
  }, []);

  const hasChanges = useMemo(() => {
    return JSON.stringify(products) !== JSON.stringify(originalProducts);
  }, [products, originalProducts]);

  const handlePriceChange = (id: string, value: string) => {
    // Only allow changes if user is admin
    if (!isAdmin) return;
    setPriceInputs(prev => ({ ...prev, [id]: value }));
  };
  
  const validatePrice = (id: number, value: string | undefined) => {
    const newErrors = { ...validationErrors };
    if (value === undefined) {
        delete newErrors[id];
        setValidationErrors(newErrors);
        return true;
    }

    const numValue = parseFloat(value);

    if (value.trim() === '') {
      newErrors[id] = "Price cannot be blank";
    } else if (isNaN(numValue)) {
      newErrors[id] = "Please enter a valid price";
    } else if (numValue < 0) {
        newErrors[id] = "Price must be a non-negative number.";
    } else if (!/^\d*\.?\d{0,2}$/.test(value)) {
      newErrors[id] = "Maximum 2 decimal places allowed.";
    } else {
      delete newErrors[id];
    }
    setValidationErrors(newErrors);
    return !newErrors[id];
  };

  const handlePriceBlur = (id: number) => {
    // Only allow changes if user is admin
    if (!isAdmin) return;
    
    const value = priceInputs[id];
    const isValid = validatePrice(id, value);

    if (isValid && value !== undefined) {
      const numValue = parseFloat(value);
      const newProducts = products.map(p => 
        p.id === id ? { ...p, standardPrice: numValue } : p
      );
      setProducts(newProducts);
      setPriceInputs(prev => ({...prev, [id]: numValue.toFixed(2)}));
    }
  };


  const handleSave = async () => {
    // Only allow save if user is admin
    if (!isAdmin) return;
    
    let allValid = true;
    Object.keys(priceInputs).forEach(id => {
        if (!validatePrice(Number(id), priceInputs[id])) {
            allValid = false;
        }
    });

    if (!allValid) {
      return;
    }
    
    setIsLoading(true);
    
    try {
      // Get products that have been modified
      const modifiedProducts = products.filter((p, index) => 
        p.standardPrice !== originalProducts[index].standardPrice
      );
      
      // Store the count before we update the state
      const updateCount = modifiedProducts.length;
      
      // Update each modified product via API
      const updatePromises = modifiedProducts.map(product => 
        postUpdatePricingStructure({
          id: product.id,
          standardPricing: product.standardPrice
        })
      );
      
      await Promise.all(updatePromises);
      
      // Update the UI with successful changes
      const now = new Date();
      const updatedProducts = products.map((p, index) => {
        if (p.standardPrice !== originalProducts[index].standardPrice) {
          return { 
            ...p, 
            lastUpdated: now.toLocaleString('en-US', { 
              month: '2-digit', 
              day: '2-digit', 
              year: 'numeric', 
              hour: '2-digit', 
              minute: '2-digit', 
              hour12: true 
            }) 
          };
        }
        return p;
      });
      
      setProducts(updatedProducts);
      setOriginalProducts(JSON.parse(JSON.stringify(updatedProducts)));
      setLastUpdateCount(updateCount);
      setIsLoading(false);
      setShowSuccessAlert(true);
      setTimeout(() => setShowSuccessAlert(false), 5000);
      
    } catch (error) {
      console.error("Failed to update pricing structures:", error);
      setIsLoading(false);
      setErrorMessage("Failed to update pricing structures. Please try again.");
      setShowErrorAlert(true);
      setTimeout(() => setShowErrorAlert(false), 5000);
    }
  };

  const handleCancel = () => {
    // Only allow cancel if user is admin
    if (!isAdmin) return;
    
    setProducts(originalProducts.map(p => ({ ...p })));
    setValidationErrors({});
    const initialInputs = originalProducts.reduce((acc, p) => {
      acc[p.id] = p.standardPrice.toFixed(2);
      return acc;
    }, {} as Record<string, string>);
    setPriceInputs(initialInputs);
  };

  // const modifiedCount = useMemo(() => {
  //   return products.filter((p, index) => p.standardPrice !== originalProducts[index].standardPrice).length;
  // }, [products, originalProducts]);

  return (
    <TooltipProvider>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            Product Pricing Management
            {!isAdmin && (
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Eye className="h-4 w-4" />
                <span>(Read-only view)</span>
              </div>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {!isAdmin && (
            <Alert className="mb-4 bg-blue-50 border-blue-200 text-blue-800">
              <Lock className="h-4 w-4" />
              <AlertTitle>Read-Only Access</AlertTitle>
              <AlertDescription>
                You are viewing pricing information in read-only mode. Only users with 'bpo-admin' privileges can modify prices.
              </AlertDescription>
            </Alert>
          )}
          
          {showSuccessAlert && isAdmin && (
            <Alert variant="default" className="mb-4 bg-green-50 border-green-200 text-green-800">
              <CheckCircle className="h-4 w-4" />
              <AlertTitle>Success!</AlertTitle>
              <AlertDescription>
                Standard pricing updated successfully for {lastUpdateCount} product(s).
              </AlertDescription>
            </Alert>
          )}
          
          {showErrorAlert && isAdmin && (
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>
                {errorMessage}
              </AlertDescription>
            </Alert>
          )}
          
          {Object.keys(validationErrors).length > 0 && isAdmin && (
             <Alert variant="destructive" className="mb-4">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Validation Errors</AlertTitle>
                <AlertDescription>
                  Please correct invalid price entries before saving.
                </AlertDescription>
              </Alert>
          )}
          
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Product Name</TableHead>
                  <TableHead>Product ID</TableHead>
                  <TableHead>Product Code</TableHead>
                  <TableHead>Standard Price</TableHead>
                  <TableHead>Last Updated</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {products.map((product) => {
                  const isModified = product.standardPrice !== originalProducts.find(p => p.id === product.id)?.standardPrice;
                  return (
                  <TableRow key={product.id} className={isModified && isAdmin ? "bg-blue-50 dark:bg-blue-900/20" : ""}>
                    <TableCell className="font-medium">{product.name}</TableCell>
                    <TableCell>{product.id}</TableCell>
                    <TableCell>{product.productCode}</TableCell>
                    <TableCell>
                      <div className="relative">
                        <DollarSign className="absolute left-2 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                        {isAdmin ? (
                          <Input
                            type="text"
                            value={priceInputs[String(product.id)] || ''}
                            onChange={(e) => handlePriceChange(String(product.id), e.target.value)}
                            onBlur={() => handlePriceBlur(product.id)}
                            className={`pl-7 ${validationErrors[String(product.id)] ? 'border-red-500' : ''}`}
                            disabled={isLoading}
                          />
                        ) : (
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <div className="pl-7 pr-3 py-2 border border-gray-200 rounded-md bg-gray-50 text-gray-700 cursor-not-allowed">
                                {product.standardPrice.toFixed(2)}
                                <Lock className="inline h-3 w-3 ml-2 text-gray-400" />
                              </div>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Price editing requires admin privileges</p>
                            </TooltipContent>
                          </Tooltip>
                        )}
                      </div>
                      {validationErrors[String(product.id)] && isAdmin && (
                        <p className="text-xs text-red-600 mt-1">{validationErrors[String(product.id)]}</p>
                      )}
                    </TableCell>
                    <TableCell>{product.lastUpdated}</TableCell>
                  </TableRow>
                )})}
              </TableBody>
            </Table>
          </div>
        </CardContent>
        
        {isAdmin && (
          <CardFooter className="flex justify-end gap-x-2">
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button variant="outline" disabled={!hasChanges || isLoading}>Cancel</Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                    <AlertDialogDescription>
                      This will discard all unsaved changes. This action cannot be undone.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>No, continue editing</AlertDialogCancel>
                    <AlertDialogAction onClick={handleCancel}>Yes, discard changes</AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
              <Button onClick={handleSave} disabled={!hasChanges || isLoading || Object.keys(validationErrors).length > 0}>
                {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Save Changes
              </Button>
          </CardFooter>
        )}
        
        {!isAdmin && (
          <CardFooter className="flex justify-center">
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="outline" disabled className="cursor-not-allowed">
                  <Lock className="h-4 w-4 mr-2" />
                  Editing Disabled
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Contact your administrator for pricing modification access</p>
              </TooltipContent>
            </Tooltip>
          </CardFooter>
        )}
      </Card>
    </TooltipProvider>
  );
};

export default ProductPricing; 