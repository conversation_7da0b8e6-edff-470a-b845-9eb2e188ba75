// Header.tsx
import React, { useState } from "react";
import { useAtom } from "jotai";
import { userAtom } from "../../jotai/user";
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuPortal,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
} from "../ui/dropdown-menu";
import { LogOut, Moon, Sun, Laptop, User } from "lucide-react";
import { useTheme } from "./ThemeProvider";
import { Link } from "react-router-dom";
import ProfileModal from "./ProfileModal";
import { getUserToken } from "../../services/request";

interface HeaderProps {
  signOut?: () => void;
}

const Header: React.FC<HeaderProps> = ({ signOut }) => {
  const [user] = useAtom(userAtom);
  const { theme, setTheme } = useTheme();
  const [isProfileModalOpen, setIsProfileModalOpen] = useState(false);

  // Get first letter of user's login ID for avatar fallback
  const getInitial = () => {
    const loginId = user?.signInDetails?.loginId || "User";
    return loginId.charAt(0).toUpperCase();
  };

  // Get user's full name or login ID
  const getUserName = () => {
    return user?.signInDetails?.loginId || "User";
  };

  const handleProfileClick = () => {
    setIsProfileModalOpen(true);
  };

  const handleProfileModalClose = () => {
    setIsProfileModalOpen(false);
  };

  const handleCMAClick = async (e: React.MouseEvent<HTMLAnchorElement>) => {
    e.preventDefault();
    try {
      const token = await getUserToken("id"); // Changed from "access" to "id"
      if (token) {
        const url = `https://simple-cma.locatealpha.com`;
        window.location.href = url;
      } else {
        console.error("Unable to retrieve ID token");
        // Fallback to URL without token
        window.location.href = "https://simple-cma.locatealpha.com/";
      }
    } catch (error) {
      console.error("Error retrieving token:", error);
      // Fallback to URL without token
      window.location.href = "https://simple-cma.locatealpha.com/";
    }
  };

  return (
    <header className="w-full bg-background border-b border-border shadow-sm">
      <div className="flex justify-between items-center py-3 px-2">
        <div className="flex justify-start items-center gap-x-4">
          <Link to={"/"}>
            <div className="flex items-center">
              <img
                src="https://platlabs.locatealpha.com/images/logo/platlabs.png"
                alt="Company Logo"
                className="h-10 w-auto dark:invert dark:brightness-0 dark:contrast-100"
              />
              <h1 className="ml-4 text-xl text-foreground select-none font-semibold">BPO</h1>
            </div>
          </Link>
          <nav className="items-center space-x-8 pl-12">
            <Link
              to="/customer"
              className="text-foreground hover:text-accent transition-colors font-medium"
            >
            Customer
            </Link>
            <Link
              to="/order"
              className="text-foreground hover:text-accent transition-colors font-medium"
            >
              Orders
            </Link>
            <Link
              to="/product-pricing"
              className="text-foreground hover:text-accent transition-colors font-medium"
            >
              Product Pricing
            </Link>
            <a
              href="https://simple-cma.locatealpha.com/"
              target="_blank"
              onClick={handleCMAClick}
              className="text-foreground hover:text-accent transition-colors font-medium"
            >
              Comparative Market Analysis
            </a>
          </nav>
        </div>

        {/* User info and avatar on the right */}
        <div className="flex items-center space-x-4 pr-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Avatar className="cursor-pointer h-9 w-9 hover:ring-2 hover:ring-primary hover:ring-offset-2 transition-all">
                <AvatarImage src="" alt={getUserName()} />
                <AvatarFallback className="bg-primary text-primary-foreground">
                  {getInitial()}
                </AvatarFallback>
              </Avatar>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <DropdownMenuLabel>{getUserName()}</DropdownMenuLabel>
              <DropdownMenuSeparator />
              
              <DropdownMenuItem className="cursor-pointer" onClick={handleProfileClick}>
                <User className="mr-2 h-4 w-4" />
                <span>Profile</span>
              </DropdownMenuItem>

              {/* Theme Toggle Submenu */}
              <DropdownMenuSub>
                <DropdownMenuSubTrigger>
                  <span className="flex items-center">
                    {theme === "dark" ? (
                      <Moon className="mr-2 h-4 w-4" />
                    ) : theme === "system" ? (
                      <Laptop className="mr-2 h-4 w-4" />
                    ) : (
                      <Sun className="mr-2 h-4 w-4" />
                    )}
                    <span>Theme</span>
                  </span>
                </DropdownMenuSubTrigger>
                <DropdownMenuPortal>
                  <DropdownMenuSubContent>
                    <DropdownMenuRadioGroup value={theme} onValueChange={setTheme}>
                      <DropdownMenuRadioItem value="light" className="cursor-pointer">
                        <Sun className="mr-2 h-4 w-4" />
                        <span>Light</span>
                      </DropdownMenuRadioItem>
                      <DropdownMenuRadioItem value="dark" className="cursor-pointer">
                        <Moon className="mr-2 h-4 w-4" />
                        <span>Dark</span>
                      </DropdownMenuRadioItem>
                      <DropdownMenuRadioItem value="system" className="cursor-pointer">
                        <Laptop className="mr-2 h-4 w-4" />
                        <span>System</span>
                      </DropdownMenuRadioItem>
                    </DropdownMenuRadioGroup>
                  </DropdownMenuSubContent>
                </DropdownMenuPortal>
              </DropdownMenuSub>

              <DropdownMenuSeparator />
              <DropdownMenuItem
                className="cursor-pointer text-destructive focus:text-destructive"
                onClick={signOut}
              >
                <LogOut className="mr-2 h-4 w-4" />
                <span>Sign out</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Profile Modal */}
      <ProfileModal 
        isOpen={isProfileModalOpen} 
        onClose={handleProfileModalClose}
      />
    </header>
  );
};

export default Header;
