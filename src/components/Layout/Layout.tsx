// Layout.tsx
import { Amplify } from "aws-amplify";
import { useAtom } from "jotai";
import { Authenticator, ThemeProvider, View, useAuthenticator } from "@aws-amplify/ui-react";
import "@aws-amplify/ui-react/styles.css";
import { useState, useEffect } from "react";
import { Modal } from "antd";
import Header from "./Header";

import { AWSAmpliyfyConfig } from "./Amplify/config";
import { defaultTheme } from "./Amplify/theme";
import { userAtom } from "../../jotai/user";
import SignUp from "./Terms/TOUModal";
import { fetchAuthSession } from "aws-amplify/auth";
import { createUserForm } from "./Amplify/form";
import { Button } from "../ui/button";
import Footer from "./Footer";
import { Toaster } from "sonner";
// Define the AuthFormData type or import it if available
interface AuthFormData {
  email?: string;
  password?: string;
  given_name?: string;
  family_name?: string;
  acknowledgement?: boolean;
  [key: string]: any;
}

// Configure Amplify
Amplify.configure(AWSAmpliyfyConfig, { ssr: true });

// Custom SignIn footer component
function CustomSignInFooter() {
  const { toForgotPassword } = useAuthenticator();
  const [isPrivacyModalOpen, setIsPrivacyModalOpen] = useState(false);
  const [isTermsModalOpen, setIsTermsModalOpen] = useState(false);

  return (
    <div className="text-sm text-[#057d95]">
      <View textAlign="center">
        <Button variant="ghost" onClick={toForgotPassword} size="sm" type="button">
          Forgot Password?
        </Button>
      </View>

      <View textAlign="center">
        <div style={{ display: "flex", justifyContent: "center" }}>
          <Button variant="ghost" onClick={() => setIsTermsModalOpen(true)} size="sm" type="button">
            Terms of Service
          </Button>

          <Button
            variant="ghost"
            onClick={() => setIsPrivacyModalOpen(true)}
            size="sm"
            type="button"
          >
            Privacy Policy
          </Button>
        </div>
      </View>

      {/* Privacy Policy Modal */}
      <Modal
        title="Privacy Policy"
        open={isPrivacyModalOpen}
        onOk={() => setIsPrivacyModalOpen(false)}
        onCancel={() => setIsPrivacyModalOpen(false)}
        width={800}
        footer={[
          <Button
            key="close"
            onClick={() => setIsPrivacyModalOpen(false)}
            variant="ghost"
            type="button"
          >
            Close
          </Button>,
        ]}
      >
        <p>Privacy policy content would go here.</p>
      </Modal>

      {/* Terms of Service Modal */}
      <Modal
        title="Terms of Service"
        open={isTermsModalOpen}
        onOk={() => setIsTermsModalOpen(false)}
        onCancel={() => setIsTermsModalOpen(false)}
        width={800}
        footer={[
          <Button
            key="close"
            onClick={() => setIsTermsModalOpen(false)}
            variant="ghost"
            type="button"
          >
            Close
          </Button>,
        ]}
      >
        <p>Terms of service content would go here.</p>
      </Modal>
    </div>
  );
}

// Custom SignIn header with logo
function CustomSignInHeader() {
  return (
    <View textAlign="center">
      <div className="flex justify-center">
        <img
          alt="Company Logo"
          src="https://platlabs.locatealpha.com/images/logo/platlabs.png"
          width="200"
        />
      </div>
    </View>
  );
}

// Protected content that shows after authentication
function AuthenticatedContent({
  children,
  signOut,
}: {
  children: React.ReactNode;
  signOut: () => void;
}) {
  const [_, setUser] = useAtom(userAtom);

  // Set user data and handle loading state
  // useEffect(() => {
  //   if (user) {
  //     setUser(user);
  //   }
  // }, [user, setUser]);
  useEffect(() => {
    const fetchUser = async () => {
      try {
        const result = await fetchAuthSession();
        console.log('CHAT fetch user', result);
        setUser({
          userId: result?.userSub || '',
          userGroup: result?.tokens?.accessToken?.payload['cognito:groups'] as string[] | undefined,
          userEmail: result?.tokens?.signInDetails?.loginId || '',
        });
      } catch (error) {
        console.error('Error fetching user session:', error);
      }
    };
    fetchUser();
  }, [setUser]);


  return (
    <div className="">
      <Header signOut={signOut} />
      <main>{children}</main>
      <Footer />
    </div>
  );
}

const Layout = ({ children }: React.PropsWithChildren) => {
  return (
    <div
      style={{
        backgroundImage: "url(/background.jpg)",
        backgroundSize: "cover",
        backgroundPosition: "center",
        minHeight: "100vh",
        alignItems: "center",
        justifyContent: "center",
      }}
    >
      <ThemeProvider theme={defaultTheme}>
        <Authenticator
          hideSignUp={false}
          initialState="signIn"
          variation="modal"
          signUpAttributes={["given_name", "family_name", "email"]}
          formFields={createUserForm}
          components={{
            SignIn: {
              Header: CustomSignInHeader,
              Footer: CustomSignInFooter,
            },
            SignUp: {
              FormFields: () => <SignUp />,
            },
          }}
          services={{
            async validateCustomSignUp(formData: AuthFormData): Promise<any> {
              // Add console logs to debug the form data
              console.log("Form data received:", formData);

              // Check if acknowledgement exists and is false or undefined
              if (!formData.acknowledgement) {
                console.log("Validation failed: acknowledgement is required");
                return {
                  acknowledgement: "You must agree to the Terms of Service and Privacy Policy",
                };
              }

              console.log("Validation passed");
              return {}; // Return empty object for no errors
            },
          }}
        >
          {({ signOut, user }) =>
            // Add null checks for signOut and user
            user && signOut ? (
              <AuthenticatedContent signOut={signOut}>
                {children}
              </AuthenticatedContent>
            ) : (
              // Fallback if signOut or user is undefined
              <div>Loading...</div>
            )
          }
        </Authenticator>
        <Toaster richColors position="top-right" />
      </ThemeProvider>
    </div>
  );
};

export default Layout;
