import React, { useState } from "react";
import { use<PERSON>tom } from "jotai";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "../ui/dialog";
import { Button } from "../ui/button";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";
import { Card, CardContent, CardHeader, CardTitle } from "../ui/card";
import { Badge } from "../ui/badge";
import { Alert, AlertDescription } from "../ui/alert";
import { User, Lock, Eye, EyeOff } from "lucide-react";
import { userAtom } from "../../jotai/user";
import { updateUserPassword } from "../../services/data";

interface ProfileModalProps {
  isOpen: boolean;
  onClose: () => void;
}

interface PasswordFormData {
  newPassword: string;
  confirmPassword: string;
}

interface PasswordErrors {
  newPassword?: string;
  confirmPassword?: string;
  general?: string;
}

const ProfileModal: React.FC<ProfileModalProps> = ({ isOpen, onClose }) => {
      // Fetch user info
  const [user] = useAtom(userAtom);
  
  const [passwordForm, setPasswordForm] = useState<PasswordFormData>({
    newPassword: "",
    confirmPassword: ""
  });
  const [passwordErrors, setPasswordErrors] = useState<PasswordErrors>({});
  const [isUpdatingPassword, setIsUpdatingPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [passwordUpdateSuccess, setPasswordUpdateSuccess] = useState(false);

  // Get first letter of user's email for avatar fallback
  const getInitial = () => {
    const email = user?.userEmail || "User";
    return email.charAt(0).toUpperCase();
  };

  // Get user's email
  const getUserName = () => {
    return user?.userEmail || "User";
  };

  // Get user group/role from Cognito groups
  const getUserGroup = () => {
    console.log("user", user);
    if (user?.userGroup) {
      // Handle both string and string[] cases
      const groups = Array.isArray(user.userGroup) ? user.userGroup : [user.userGroup];
      
      // Check for admin groups first
      if (groups.includes('bpo-admin')){
        return "Admin";
      }
      
      // Check for customer service representative
      if (groups.includes('bpo-csr')) {
        return "Customer Service Representative";
      }
      
      // Return the original group names if no mapping found
      return groups.join(", ");
    }
    return "User"; // Default role
  };

  // Password validation
  const validatePassword = (password: string): string[] => {
    const errors: string[] = [];
    if (password.length < 8) {
      errors.push("Password must be at least 8 characters long");
    }
    if (!/(?=.*[a-z])/.test(password)) {
      errors.push("Password must contain at least one lowercase letter");
    }
    if (!/(?=.*[A-Z])/.test(password)) {
      errors.push("Password must contain at least one uppercase letter");
    }
    if (!/(?=.*\d)/.test(password)) {
      errors.push("Password must contain at least one number");
    }
    return errors;
  };

  // Handle password form input changes
  const handlePasswordInputChange = (field: keyof PasswordFormData, value: string) => {
    setPasswordForm(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear errors for this field when user starts typing
    if (passwordErrors[field]) {
      setPasswordErrors(prev => ({
        ...prev,
        [field]: undefined
      }));
    }
    
    // Clear success message when user starts typing
    if (passwordUpdateSuccess) {
      setPasswordUpdateSuccess(false);
    }
  };

  // Handle password update submission
  const handlePasswordUpdate = async () => {
    const errors: PasswordErrors = {};

    // Validate new password
    if (!passwordForm.newPassword) {
      errors.newPassword = "New password is required";
    } else {
      const validationErrors = validatePassword(passwordForm.newPassword);
      if (validationErrors.length > 0) {
        errors.newPassword = validationErrors[0]; // Show first error
      }
    }

    // Validate confirm password
    if (!passwordForm.confirmPassword) {
      errors.confirmPassword = "Please confirm your new password";
    } else if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      errors.confirmPassword = "Passwords do not match";
    }

    setPasswordErrors(errors);

    // If no errors, proceed with update
    if (Object.keys(errors).length === 0) {
      setIsUpdatingPassword(true);
      
      try {
        // Get the username from user email for the API call
        const username = user?.userEmail;
        
        if (!username) {
          throw new Error("User email not found");
        }
        
        // Call the actual API to update password
        await updateUserPassword(username, passwordForm.newPassword);
        
        // Reset form on success
        setPasswordForm({
          newPassword: "",
          confirmPassword: ""
        });
        
        // Clear any previous errors and show success message
        setPasswordErrors({});
        setPasswordUpdateSuccess(true);
        
        console.log("Password updated successfully");
        
        // Hide success message after 3 seconds
        setTimeout(() => {
          setPasswordUpdateSuccess(false);
        }, 3000);
        
      } catch (error) {
        console.error("Password update error:", error);
        setPasswordErrors({
          general: "Failed to update password. Please try again."
        });
      } finally {
        setIsUpdatingPassword(false);
      }
    }
  };

  // Reset form when modal closes
  const handleClose = () => {
    setPasswordForm({
      newPassword: "",
      confirmPassword: ""
    });
    setPasswordErrors({});
    setPasswordUpdateSuccess(false);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>User Profile</DialogTitle>
          <DialogDescription>
            View and manage your profile information
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* User Avatar and Basic Info */}
          <div className="flex flex-col items-center space-y-4">
            <Avatar className="h-20 w-20">
              <AvatarImage src="" alt={getUserName()} />
              <AvatarFallback className="bg-primary text-primary-foreground text-2xl">
                {getInitial()}
              </AvatarFallback>
            </Avatar>
            
            <div className="text-center">
              <h3 className="text-lg font-semibold">{getUserName()}</h3>
              <Badge variant="secondary">{getUserGroup()}</Badge>
            </div>
          </div>

          {/* Profile Information Cards */}
          <div className="space-y-4">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center space-x-2">
                  <User className="h-4 w-4" />
                  <span>Account Information</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Email:</span>
                  <span className="text-sm font-medium">{getUserName()}</span>
                </div>
             
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Status:</span>
                  <Badge variant="default">Active</Badge>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center space-x-2">
                  <Lock className="h-4 w-4" />
                  <span>Update Password</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {passwordErrors.general && (
                  <Alert variant="destructive">
                    <AlertDescription>{passwordErrors.general}</AlertDescription>
                  </Alert>
                )}
                
                {passwordUpdateSuccess && (
                  <Alert variant="default" className="border-green-200 bg-green-50 text-green-800">
                    <AlertDescription>Password updated successfully!</AlertDescription>
                  </Alert>
                )}

                <div className="space-y-2">
                  <Label htmlFor="newPassword">New Password</Label>
                  <div className="relative">
                    <Input
                      id="newPassword"
                      type={showNewPassword ? "text" : "password"}
                      value={passwordForm.newPassword}
                      onChange={(e) => handlePasswordInputChange("newPassword", e.target.value)}
                      className={passwordErrors.newPassword ? "border-destructive" : ""}
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                      onClick={() => setShowNewPassword(!showNewPassword)}
                    >
                      {showNewPassword ? (
                        <EyeOff className="h-4 w-4" />
                      ) : (
                        <Eye className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                  {passwordErrors.newPassword && (
                    <p className="text-sm text-destructive">{passwordErrors.newPassword}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="confirmPassword">Confirm New Password</Label>
                  <div className="relative">
                    <Input
                      id="confirmPassword"
                      type={showConfirmPassword ? "text" : "password"}
                      value={passwordForm.confirmPassword}
                      onChange={(e) => handlePasswordInputChange("confirmPassword", e.target.value)}
                      className={passwordErrors.confirmPassword ? "border-destructive" : ""}
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    >
                      {showConfirmPassword ? (
                        <EyeOff className="h-4 w-4" />
                      ) : (
                        <Eye className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                  {passwordErrors.confirmPassword && (
                    <p className="text-sm text-destructive">{passwordErrors.confirmPassword}</p>
                  )}
                </div>

                <Button 
                  onClick={handlePasswordUpdate} 
                  disabled={isUpdatingPassword}
                  className="w-full"
                >
                  {isUpdatingPassword ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                      Updating...
                    </>
                  ) : (
                    "Update Password"
                  )}
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>

        <DialogFooter>
          <Button type="button" variant="outline" onClick={handleClose}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ProfileModal; 