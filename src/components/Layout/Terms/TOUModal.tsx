// TOUModal.tsx
import { useState, useEffect, useRef } from 'react';
import { Authenticator, View, useAuthenticator } from "@aws-amplify/ui-react";
import { Checkbox } from "@/components/ui/checkbox";
import { Button } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON>Footer,
  <PERSON>alogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

const SignUp = () => {
  const [isPrivacyModalOpen, setIsPrivacyModalOpen] = useState(false);
  const [isTermsModalOpen, setIsTermsModalOpen] = useState(false);
  const [acknowledgement, setAcknowledgement] = useState(false);
  
  // Get access to the form data
  const { updateForm } = useAuthenticator();
  
  // Use a ref to prevent infinite updates
  const isInitialMount = useRef(true);
  const hasUpdatedForm = useRef(false);

  // Update the form data when acknowledgement changes
  useEffect(() => {
    // Skip the first render
    if (isInitialMount.current) {
      isInitialMount.current = false;
      return;
    }
    
    // Only update form once per acknowledgement change
    if (hasUpdatedForm.current !== acknowledgement) {
      hasUpdatedForm.current = acknowledgement;
      
      // Update the form with the acknowledgement value
      updateForm({
        acknowledgement: acknowledgement
      });
    }
  }, [acknowledgement]);

  // Handle checkbox change manually
  const handleCheckboxChange = (checked: boolean) => {
    setAcknowledgement(checked === true);
  };

  const handleTermsClick = (e: React.MouseEvent<HTMLAnchorElement>) => {
    e.preventDefault();
    setIsTermsModalOpen(true);
  };

  const handlePrivacyClick = (e: React.MouseEvent<HTMLAnchorElement>) => {
    e.preventDefault();
    setIsPrivacyModalOpen(true);
  };

  return (
    <>
      {/* Use default form fields from Authenticator */}
      <Authenticator.SignUp.FormFields />
      
      {/* Add checkbox for terms acceptance */}
      <View padding="0 1rem 1rem">
        <div className="flex items-start space-x-2 mt-4">
          <Checkbox
            id="acknowledgement"
            name="acknowledgement"
            checked={acknowledgement}
            onCheckedChange={handleCheckboxChange}
            className="mt-1"
          />
          <label 
            htmlFor="acknowledgement" 
            className="text-sm text-gray-600 leading-relaxed"
          >
            I agree to the{" "}
            <a 
              href="#" 
              onClick={handleTermsClick}
              className="text-blue-600 hover:text-blue-800 hover:underline"
            >
              Terms of Service
            </a>{" "}
            and{" "}
            <a 
              href="#" 
              onClick={handlePrivacyClick}
              className="text-blue-600 hover:text-blue-800 hover:underline"
            >
              Privacy Policy
            </a>
          </label>
        </div>
      </View>
      
      {/* Privacy Policy Dialog */}
      <Dialog open={isPrivacyModalOpen} onOpenChange={setIsPrivacyModalOpen}>
        <DialogContent className="sm:max-w-[800px] max-h-[80vh] overflow-auto">
          <DialogHeader>
            <DialogTitle>Privacy Policy</DialogTitle>
          </DialogHeader>
          
          <div className="py-4">
            <h2 className="text-xl font-semibold mb-4">Privacy Policy</h2>
            
            <p className="mb-4">Last updated: April 10, 2025</p>
            
            <p className="mb-4">This Privacy Policy describes how we collect, use, and handle your personal information when you use our services.</p>
            
            <h3 className="text-lg font-medium mt-6 mb-2">Information We Collect</h3>
            
            <p className="mb-2">We collect information in the following ways:</p>
            
            <ul className="list-disc pl-6 mb-4 space-y-1">
              <li>Information you provide to us directly when you create an account or use our services</li>
              <li>Information we collect about your usage of our platform</li>
              <li>Information we receive from third parties</li>
            </ul>
            
            <h3 className="text-lg font-medium mt-6 mb-2">How We Use Your Information</h3>
            
            <p className="mb-2">We use the information we collect to:</p>
            
            <ul className="list-disc pl-6 mb-4 space-y-1">
              <li>Provide, maintain, and improve our services</li>
              <li>Develop new products and features</li>
              <li>Protect our users and the public</li>
              <li>Communicate with you about our services</li>
            </ul>
          </div>
          
          <DialogFooter>
            <Button type="button" onClick={() => setIsPrivacyModalOpen(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Terms of Service Dialog */}
      <Dialog open={isTermsModalOpen} onOpenChange={setIsTermsModalOpen}>
        <DialogContent className="sm:max-w-[800px] max-h-[80vh] overflow-auto">
          <DialogHeader>
            <DialogTitle>Terms of Service</DialogTitle>
          </DialogHeader>
          
          <div className="py-4">
            <h2 className="text-xl font-semibold mb-4">Terms of Service</h2>
            
            <p className="mb-4">Last updated: April 10, 2025</p>
            
            <p className="mb-4">Please read these Terms of Service carefully before using our services.</p>
            
            <h3 className="text-lg font-medium mt-6 mb-2">Acceptance of Terms</h3>
            
            <p className="mb-4">By creating an account and using our services, you agree to be bound by these Terms of Service and our Privacy Policy.</p>
            
            <h3 className="text-lg font-medium mt-6 mb-2">User Accounts</h3>
            
            <p className="mb-4">You are responsible for maintaining the confidentiality of your account credentials and for all activities that occur under your account.</p>
            
            <h3 className="text-lg font-medium mt-6 mb-2">Acceptable Use</h3>
            
            <p className="mb-2">You agree not to use our services to:</p>
            
            <ul className="list-disc pl-6 mb-4 space-y-1">
              <li>Violate any laws or regulations</li>
              <li>Infringe on the rights of others</li>
              <li>Distribute malware or engage in harmful activities</li>
              <li>Attempt to gain unauthorized access to our systems</li>
            </ul>
          </div>
          
          <DialogFooter>
            <Button type="button" onClick={() => setIsTermsModalOpen(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default SignUp;