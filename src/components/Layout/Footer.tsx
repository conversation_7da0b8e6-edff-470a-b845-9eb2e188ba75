// Footer.tsx

const Footer = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="w-full bg-card text-card-foreground border-t border-border">
      {/* Main Footer Content */}
      <div className="container mx-auto px-4 py-10">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
          <div className="col-span-2">
            <div className="flex items-center mb-4">
              <img
                src="https://platlabs.locatealpha.com/images/logo/platlabs.png"
                alt="Company Logo"
                className="h-8 w-auto mr-2 dark:invert dark:brightness-0 dark:contrast-100"
              />
              <span className="text-lg font-semibold">BPO Platform</span>
            </div>
            <p className="text-sm max-w-md mb-6">
              Dummmmmy Text
            </p>
            <div className="flex flex-col space-y-2">
              <div className="flex items-center">
                <span className="text-sm w-24">Email:</span>
                <a href="mailto:<EMAIL>" className="text-sm hover:text-accent transition-colors">
                  <EMAIL>
                </a>
              </div>
              <div className="flex items-center">
                <span className="text-sm w-24">Phone:</span>
                <a href="tel:+11234567890" className="text-sm hover:text-accent transition-colors">
                  +****************
                </a>
              </div>
              <div className="flex items-center">
                <span className="text-sm w-24">Address:</span>
                <span className="text-sm">
                  123 Market Street, Suite 100, San Francisco, CA 94103
                </span>
              </div>
            </div>
          </div>
          <div>
            <h3 className="font-medium mb-4">Quick Links</h3>
            <ul className="space-y-2">
              <li>
                <a href="/" className="text-sm hover:text-accent transition-colors">Home</a>
              </li>
              <li>
                <a href="/customer" className="text-sm hover:text-accent transition-colors">Customer Search</a>
              </li>
              <li>
                <a href="/order" className="text-sm hover:text-accent transition-colors">Orders</a>
              </li>
              <li>
                <a href="/product-pricing" className="text-sm hover:text-accent transition-colors">Product Pricing</a>
              </li>
            </ul>
          </div>
          <div>
            <h3 className="font-medium mb-4">Documents</h3>
            <ul className="space-y-2">
              <li>
                <a href="/dashboard" className="text-sm hover:text-accent transition-colors">Term of Use</a>
              </li>
              <li>
                <a href="/dashboard" className="text-sm hover:text-accent transition-colors">2</a>
              </li>
              <li>
                <a href="/dashboard" className="text-sm hover:text-accent transition-colors">3</a>
              </li>
              <li>
                <a href="/dashboard" className="text-sm hover:text-accent transition-colors">4</a>
              </li>
              <li>
                <a href="/dashboard" className="text-sm hover:text-accent transition-colors">5</a>
              </li>
            </ul>
          </div>
          
        </div>
      </div>
      
      {/* Copyright Section */}
      <div className="border-t border-border">
        <div className="container mx-auto px-4 py-6">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-xs">
              © {currentYear} Platlabs. All rights reserved.
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;