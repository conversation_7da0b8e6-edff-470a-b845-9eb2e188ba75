export interface ClientCustomer {
    id: number | null;
    sourceAcctNo: number;
    company: string;
    bAddress1: string;
    bAddress2?: string;
    bCity: string;
    bState: string;
    bZip: string;
    county: string;
    industryId: number | null;
    isActiveFlag: boolean;
    isTaxableFlag: boolean;
    creditCardFlag: boolean;
    oracleSalesPersonId: number | null;
    classification: boolean | null;
    contractSummary: string | null;
    secondaryOracleSalesPersonId: number | null;
    consolidatedBillingFlag: number | null;
    consolidatedParentId: number | null;
    statement: boolean | null;
    isBillable: boolean;
    phone: string;
    extn: string | null;
    contactName: string | null;
    attn: string | null;
    fax: string | null;
    email: string;
    agreementId: number | null;
    agreementStartDate: string | null; // Using string for dates in TypeScript
    agreementEndDate: string | null;
    term: number | null;
    unlimitedAutoRenewFlag: number | null;
    isTestData: boolean;
    pricingStructures: PricingStructureSelection[];

  }
export interface PricingStructure {
  id: number;
  name: string;
  productCode: number;
  standardPricing: string;
}

export interface PricingStructureSelection {
  id?: number;
  pricingStructureId: number;
  specialPricing: number | null; // null means no special pricing, number means special pricing value
}


