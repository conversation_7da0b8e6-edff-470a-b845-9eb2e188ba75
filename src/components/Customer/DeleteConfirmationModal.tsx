import { useAtom } from "jotai";
import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { Loader2, AlertTriangle } from "lucide-react";

import {
  isDeleteConfirmOpenAtom,
  selectedCustomerAtom,
  deleteCustomerAtom,
  isDeletingCustomerAtom,
  deleteCustomerErrorAtom,
} from "@/jotai/customers";

const DeleteConfirmationModal = () => {
  const [isOpen, setIsOpen] = useAtom(isDeleteConfirmOpenAtom);
  const [selectedCustomer] = useAtom(selectedCustomerAtom);
  const [, deleteCustomer] = useAtom(deleteCustomerAtom);
  const [isDeleting] = useAtom(isDeletingCustomerAtom);
  const [deleteError] = useAtom(deleteCustomerErrorAtom);

  const handleClose = () => {
    if (!isDeleting) {
      setIsOpen(false);
    }
  };

  const handleDelete = () => {
    deleteCustomer();
  };

  if (!selectedCustomer) return null;

  return (
    <AlertDialog open={isOpen} onOpenChange={handleClose}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-red-500" />
            Confirm Customer Deactivation
          </AlertDialogTitle>
          <AlertDialogDescription>
            Are you sure you want to deactive the customer <strong>{selectedCustomer.company}</strong>?
            <br />
          </AlertDialogDescription>
        </AlertDialogHeader>
        
        {deleteError && (
          <div className="bg-red-50 border border-red-200 rounded p-3 text-sm text-red-600">
            {deleteError}
          </div>
        )}
        
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
          <Button 
            variant="destructive" 
            onClick={handleDelete} 
            disabled={isDeleting}
          >
            {isDeleting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Deleting...
              </>
            ) : (
              "Deactive Customer"
            )}
          </Button>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

export default DeleteConfirmationModal;