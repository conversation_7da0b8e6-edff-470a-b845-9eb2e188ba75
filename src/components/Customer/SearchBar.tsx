// src/components/CustomerSearch/SearchBar.tsx
import React from "react";
import { useAtom } from "jotai";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { SearchIcon, RefreshCwIcon, PlusIcon } from "lucide-react";
import { searchTermAtom, searchCustomersAtom, isCreateModalOpenAtom } from "@/jotai/customers";
import { useCustomersFetch } from "@/jotai/hooks/useCustomersFetch";

const CustomerSearchBar = () => {
  const [searchTerm, setSearchTerm] = useAtom(searchTermAtom);
  const [, searchCustomers] = useAtom(searchCustomersAtom);
  const [, setIsCreateModalOpen] = useAtom(isCreateModalOpenAtom);

  const { isLoading, error, refetch } = useCustomersFetch();

  const handleSearch = () => {
    searchCustomers();
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleSearch();
    }
  };

  const handleRefresh = () => {
    refetch();
  };

  const handleCreateClick = () => {
    setIsCreateModalOpen(true);
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <div>
          <CardTitle>Customer Search</CardTitle>
          <CardDescription>
            Search for customers by ID, account number, company name, or email.
          </CardDescription>
        </div>
        <Button onClick={handleCreateClick} className="ml-auto">
          <PlusIcon className="mr-2 h-4 w-4" />
          New Customer
        </Button>
      </CardHeader>
      <CardContent>
        <div className="flex space-x-2">
          <div className="relative flex-1 max-w-md">
            <Input
              placeholder="Search..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onKeyPress={handleKeyPress}
              disabled={isLoading}
              className="pr-10"
            />
            {searchTerm && (
              <button
                onClick={() => setSearchTerm("")}
                className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700"
                aria-label="Clear search"
              >
                ×
              </button>
            )}
          </div>
          <Button onClick={handleSearch} disabled={isLoading || !searchTerm.trim()}>
            <SearchIcon className="mr-2 h-4 w-4" />
            Search
          </Button>
          <Button
            variant="outline"
            onClick={handleRefresh}
            disabled={isLoading}
            title="Refresh customer data"
          >
            <RefreshCwIcon className={`h-4 w-4 ${isLoading ? "animate-spin" : ""}`} />
          </Button>
        </div>

        {error && (
          <div className="mt-2 text-sm text-red-500">
            Error: {error instanceof Error ? error.message : "Unknown error occurred"}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default CustomerSearchBar;
