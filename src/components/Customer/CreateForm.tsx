import { use<PERSON><PERSON> } from "jotai";
import { useEffect, useState, useMemo } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { AlertCircle, DollarSign } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { zodResolver } from "@hookform/resolvers/zod";
import { Resolver, useForm } from "react-hook-form";
import { z } from "zod";

import {
  isCreateModalOpenAtom,
  newCustomerFormAtom,
  createCustomerAtom,
  isCreatingCustomerAtom,
  createCustomerErrorAtom,
} from "@/jotai/customers";
import { userAtom } from "@/jotai/user";
import { getAllPricingStructures } from "@/services/data";
import { PricingStructure, PricingStructureSelection } from "./types";

// Define BPO product pricing interface
interface BpoProductPricing {
  pricingStructureId: number;
  pricingType: "standard" | "special";
  price: number | null;
}


// Define the form validation schema based on provided requirements
const customerFormSchema = z.object({
  // Required fields with validation
  company: z.string().min(1, { message: "Company name is required" }),
  bAddress1: z.string().min(1, { message: "Address line 1 is required" }),
  bAddress2: z.string().optional(),
  bCity: z.string().min(1, { message: "City is required" }),
  bState: z.string().length(2, { message: "Please enter a valid state code (2 letters)" }),
  bZip: z.string().min(5, { message: "ZIP code is required" }),
  county: z.string().min(1, { message: "County is required" }),
  phone: z.string().min(10, { message: "Phone number is required" }),
  email: z.string().email({ message: "Please enter a valid email address" }),

  // Required boolean flags
  isActiveFlag: z.boolean().default(true),
  isTaxableFlag: z.boolean().default(true),
  creditCardFlag: z.boolean().default(false),
  isBillable: z.boolean().default(true),

  // Optional fields
  industryID: z.number().nullable(),
  oracleSalesPersonID: z.number().nullable(),
  classification: z.boolean().nullable(),
  secondaryOracleSalesPersonID: z.number().nullable(),
  consolidatedBillingFlag: z.number().nullable(),
  consolidatedParentId: z.number().nullable(),
  statement: z.boolean().nullable(),
  contactName: z.string().optional(),
  extn: z.string().optional(),
  attn: z.string().optional(),
  fax: z.string().optional(),
  contractSummary: z.string().optional(),

  // Agreement fields
  agreementID: z.number().nullable(),
  term: z.number().nullable(),
  agreementStartDate: z.string().nullable(),
  agreementEndDate: z.string().nullable(),
  unlimitedAutoRenewFlag: z.number().nullable(),

  // BPO Products Pricing
  bpoProductsPricing: z.array(
    z.object({
      pricingStructureId: z.number(),
      pricingType: z.enum(["standard", "special"]),
      price: z.number().nullable(),
    }).superRefine((data, ctx) => {
      if (data.pricingType === "special") {
        if (data.price === null || data.price === undefined) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: "Special price cannot be blank",
            path: ["price"],
          });
        } else if (data.price <= 0) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: "Price must be greater than 0",
            path: ["price"],
          });
        } else {
          // Check for exactly 2 decimal places
          const decimalStr = data.price.toString();
          const decimalIndex = decimalStr.indexOf('.');
          if (decimalIndex !== -1 && decimalStr.length - decimalIndex - 1 > 2) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: "Maximum 2 decimal places allowed",
              path: ["price"],
            });
          }
        }
      }
    })
  ).default([]),

  // Legacy pricing structures (keeping for backward compatibility)
  pricingStructures: z.array(
    z.object({
      pricingStructureId: z.number(),
      isSpecialPricing: z.boolean(),
    })
  ).default([]),
});

// Extract the inferred type
type CustomerFormValues = z.infer<typeof customerFormSchema>;

const CreateCustomerForm = () => {
  const [isOpen, setIsOpen] = useAtom(isCreateModalOpenAtom);
  const [, setNewCustomerForm] = useAtom(newCustomerFormAtom);
  const [, createCustomer] = useAtom(createCustomerAtom);
  const [isCreating] = useAtom(isCreatingCustomerAtom);
  const [createError] = useAtom(createCustomerErrorAtom);
  const [user] = useAtom(userAtom);
  const [pricingStructures, setPricingStructures] = useState<PricingStructure[]>([]);
  const [bpoProductsPricing, setBpoProductsPricing] = useState<BpoProductPricing[]>([]);

  // Check if user has admin privileges by looking for 'bpo-admin' in userGroup
  const isAdmin = useMemo(() => {
    if (!user?.userGroup) return false;
    
    // Handle both string array and string cases
    if (Array.isArray(user.userGroup)) {
      return user.userGroup.includes('bpo-admin');
    }
    
    return user.userGroup === 'bpo-admin';
  }, [user?.userGroup]);

  // Fetch pricing structures on component mount
  useEffect(() => {
    const fetchPricingStructures = async () => {
      try {
        const structures = await getAllPricingStructures();
        setPricingStructures(structures);
        
        // Initialize BPO products pricing with real standard prices from API
        const initialBpoProductsPricing: BpoProductPricing[] = structures.map((structure: PricingStructure) => ({
          pricingStructureId: structure.id,
          pricingType: "standard" as const,
          price: parseFloat(structure.standardPricing || "0"), // Use real standard pricing from API
        }));
        setBpoProductsPricing(initialBpoProductsPricing);
      } catch (error) {
        console.error("Failed to fetch pricing structures:", error);
      }
    };

    fetchPricingStructures();
  }, []);

  // Initialize form with default values - fixed type annotation
  const form = useForm<CustomerFormValues>({
    resolver: zodResolver(customerFormSchema) as Resolver<CustomerFormValues>,
    defaultValues: {
      company: "",
      bAddress1: "",
      bAddress2: "",
      bCity: "",
      bState: "",
      bZip: "",
      county: "",
      phone: "",
      email: "",

      // Default values for required boolean flags
      isActiveFlag: true,
      isTaxableFlag: true,
      creditCardFlag: false,
      isBillable: true,

      // Optional fields initialized as empty or null
      industryID: null,
      oracleSalesPersonID: null,
      classification: null,
      secondaryOracleSalesPersonID: null,
      consolidatedBillingFlag: null,
      consolidatedParentId: null,
      statement: null,
      contactName: "",
      extn: "",
      attn: "",
      fax: "",
      contractSummary: "",

      // Agreement fields
      agreementID: null,
      term: null,
      agreementStartDate: null,
      agreementEndDate: null,
      unlimitedAutoRenewFlag: null,

      // BPO Products Pricing
      bpoProductsPricing: [],
      
      // Pricing structures
      pricingStructures: [],
    },
  });



  // Form submission handler
  const onSubmit = async (data: CustomerFormValues) => {
    // Transform bpoProductsPricing to the expected API format
    const transformedPricingStructures: PricingStructureSelection[] = data.bpoProductsPricing.map(item => ({
      pricingStructureId: item.pricingStructureId,
      specialPricing: item.pricingType === "special" ? item.price : null
    }));

    // Create the transformed data object
    const transformedData = {
      ...data,
      pricingStructures: transformedPricingStructures
    };

    setNewCustomerForm(transformedData);
    await createCustomer();
  };

  // Draft saving handler for CSRs
  const onSaveAsDraft = async (data: CustomerFormValues) => {
    // Transform bpoProductsPricing to the expected API format
    const transformedPricingStructures: PricingStructureSelection[] = data.bpoProductsPricing.map(item => ({
      pricingStructureId: item.pricingStructureId,
      specialPricing: item.pricingType === "special" ? item.price : null
    }));

    // Create the transformed data object with draft flag
    const transformedData = {
      ...data,
      pricingStructures: transformedPricingStructures,
      isDraft: true
    };

    setNewCustomerForm(transformedData);
    await createCustomer();
  };



  // BPO Products Pricing Helper Functions
  const handlePricingTypeChange = (pricingStructureId: number, newType: "standard" | "special") => {
    const updatedPricing = bpoProductsPricing.map((item) => {
      if (item.pricingStructureId === pricingStructureId) {
        return {
          ...item,
          pricingType: newType,
          // Reset price to null when switching to special pricing for validation
          price: newType === "special" ? null : item.price,
        };
      }
      return item;
    });
    setBpoProductsPricing(updatedPricing);
    form.setValue("bpoProductsPricing", updatedPricing);
  };

  const handlePriceChange = (pricingStructureId: number, newPrice: string) => {
    const numericPrice = newPrice === "" ? null : parseFloat(newPrice);
    const updatedPricing = bpoProductsPricing.map((item) => {
      if (item.pricingStructureId === pricingStructureId) {
        return {
          ...item,
          price: numericPrice,
        };
      }
      return item;
    });
    setBpoProductsPricing(updatedPricing);
    form.setValue("bpoProductsPricing", updatedPricing);
  };

  const getBpoProductPricing = (pricingStructureId: number) => {
    return bpoProductsPricing.find((item) => item.pricingStructureId === pricingStructureId);
  };

  const getStandardPrice = (pricingStructureId: number) => {
    const structure = pricingStructures.find(s => s.id === pricingStructureId);
    // Return the real standard price from the pricing structure
    return structure ? parseFloat(structure.standardPricing || "0") : 0;
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogContent className="sm:max-w-[900px] max-h-[90vh] overflow-hidden flex flex-col text-sm">
        <DialogHeader>
          <DialogTitle>Create New Customer</DialogTitle>
          <DialogDescription>
            Fill in the customer details to create a new customer record. Fields marked with * are
            required.
          </DialogDescription>
        </DialogHeader>

        {createError && (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4 mr-2" />
            <AlertDescription>{createError}</AlertDescription>
          </Alert>
        )}

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit as any)}>
            <ScrollArea className="pr-4 h-[60vh]">
              <Tabs defaultValue="basic" className="w-full">
                <TabsList className="grid grid-cols-4 mb-4">
                  <TabsTrigger value="basic">Basic Info</TabsTrigger>
                  <TabsTrigger value="billing">Billing Options</TabsTrigger>
                  <TabsTrigger value="agreement">Agreement Details</TabsTrigger>
                  <TabsTrigger value="pricing">BPO Products Pricing</TabsTrigger>
                </TabsList>

                {/* Basic Information Tab */}
                <TabsContent value="basic" className="space-y-4">
                  <FormField
                    control={form.control as any}
                    name="company"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Company Name*</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter company name" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Address Information */}
                    <div className="space-y-4">
                      <h3 className="text-md font-medium">Billing Address</h3>

                      <FormField
                        control={form.control as any}
                        name="bAddress1"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Address Line 1*</FormLabel>
                            <FormControl>
                              <Input placeholder="Street address" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control as any}
                        name="bAddress2"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Address Line 2</FormLabel>
                            <FormControl>
                              <Input placeholder="Apt, Suite, etc." {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <div className="grid grid-cols-2 gap-4">
                        <FormField
                          control={form.control as any}
                          name="bCity"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>City*</FormLabel>
                              <FormControl>
                                <Input placeholder="City" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control as any}
                          name="bState"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>State*</FormLabel>
                              <FormControl>
                                <Input placeholder="2-letter code" maxLength={2} {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        <FormField
                          control={form.control as any}
                          name="bZip"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>ZIP Code*</FormLabel>
                              <FormControl>
                                <Input placeholder="ZIP code" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control as any}
                          name="county"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>County*</FormLabel>
                              <FormControl>
                                <Input placeholder="County" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>

                    {/* Contact Information */}
                    <div className="space-y-4">
                      <h3 className="text-md font-medium">Contact Information</h3>

                      <FormField
                        control={form.control as any}
                        name="contactName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Contact Name</FormLabel>
                            <FormControl>
                              <Input placeholder="Primary contact person" {...field} />
                            </FormControl>
                            <FormDescription>
                              Person to contact for billing purposes
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control as any}
                        name="email"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Email*</FormLabel>
                            <FormControl>
                              <Input type="email" placeholder="Official billing email" {...field} />
                            </FormControl>
                            <FormDescription>
                              Official email where invoices should be sent
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <div className="grid grid-cols-2 gap-4">
                        <FormField
                          control={form.control as any}
                          name="phone"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Phone*</FormLabel>
                              <FormControl>
                                <Input placeholder="Phone number" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control as any}
                          name="extn"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Extension</FormLabel>
                              <FormControl>
                                <Input placeholder="Ext." {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        <FormField
                          control={form.control as any}
                          name="attn"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Attention</FormLabel>
                              <FormControl>
                                <Input placeholder="Attn: Billing Department" {...field} />
                              </FormControl>
                              <FormDescription>
                                Name invoices should be addressed to
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control as any}
                          name="fax"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Fax</FormLabel>
                              <FormControl>
                                <Input placeholder="Fax number" {...field} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      <FormField
                        control={form.control as any}
                        name="industryID"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Industry ID</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                placeholder="Industry ID"
                                value={field.value?.toString() || ""}
                                onChange={(e) =>
                                  field.onChange(e.target.value ? parseInt(e.target.value) : null)
                                }
                              />
                            </FormControl>
                            <FormDescription>From FA Industry List</FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>
                </TabsContent>

                {/* Billing Tab */}
                <TabsContent value="billing" className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <h3 className="text-lg font-medium">Billing Settings</h3>

                      <FormField
                        control={form.control as any}
                        name="isActiveFlag"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-start space-x-3 space-y-0 p-4 border rounded-md">
                            <FormControl>
                              <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                            </FormControl>
                            <div className="space-y-1 leading-none">
                              <FormLabel>Active Customer*</FormLabel>
                              <FormDescription>
                                Is this customer allowed to place orders in the production system?
                              </FormDescription>
                            </div>
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control as any}
                        name="isBillable"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-start space-x-3 space-y-0 p-4 border rounded-md">
                            <FormControl>
                              <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                            </FormControl>
                            <div className="space-y-1 leading-none">
                              <FormLabel>Billable*</FormLabel>
                              <FormDescription>
                                Should this customer receive an invoice? Set to No for test
                                accounts.
                              </FormDescription>
                            </div>
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control as any}
                        name="isTaxableFlag"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-start space-x-3 space-y-0 p-4 border rounded-md">
                            <FormControl>
                              <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                            </FormControl>
                            <div className="space-y-1 leading-none">
                              <FormLabel>Taxable*</FormLabel>
                              <FormDescription>
                                Is this customer supposed to be taxed (are they a reseller)?
                              </FormDescription>
                            </div>
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control as any}
                        name="creditCardFlag"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-start space-x-3 space-y-0 p-4 border rounded-md">
                            <FormControl>
                              <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                            </FormControl>
                            <div className="space-y-1 leading-none">
                              <FormLabel>Credit Card*</FormLabel>
                              <FormDescription>
                                Should this customer be automatically charged their invoice total on
                                their credit card each month?
                              </FormDescription>
                            </div>
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control as any}
                        name="statement"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-start space-x-3 space-y-0 p-4 border rounded-md">
                            <FormControl>
                              <Checkbox
                                checked={field.value || false}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                            <div className="space-y-1 leading-none">
                              <FormLabel>Statement</FormLabel>
                              <FormDescription>
                                Should this customer receive an automated accounting statement from
                                Oracle?
                              </FormDescription>
                            </div>
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control as any}
                        name="classification"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-start space-x-3 space-y-0 p-4 border rounded-md">
                            <FormControl>
                              <Checkbox
                                checked={field.value || false}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                            <div className="space-y-1 leading-none">
                              <FormLabel>Classification</FormLabel>
                              <FormDescription>
                                Is this an internal customer? (Checked = Internal, Unchecked =
                                External)
                              </FormDescription>
                            </div>
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="space-y-4">
                      <h3 className="text-lg font-medium">Additional Information</h3>

                      <FormField
                        control={form.control as any}
                        name="contractSummary"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Contract Summary</FormLabel>
                            <FormControl>
                              <textarea
                                className="flex min-h-[120px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                placeholder="Enter contract or agreement information..."
                                {...field}
                              />
                            </FormControl>
                            <FormDescription>
                              Contract number or information associated with this account
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control as any}
                        name="oracleSalesPersonID"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Oracle Sales Person ID</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                placeholder="Sales Person ID"
                                value={field.value?.toString() || ""}
                                onChange={(e) =>
                                  field.onChange(e.target.value ? parseInt(e.target.value) : null)
                                }
                              />
                            </FormControl>
                            <FormDescription>
                              Sales rep who should earn commissions on this sale
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control as any}
                        name="secondaryOracleSalesPersonID"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Secondary Oracle Sales Person ID</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                placeholder="Secondary Sales Person ID"
                                value={field.value?.toString() || ""}
                                onChange={(e) =>
                                  field.onChange(e.target.value ? parseInt(e.target.value) : null)
                                }
                              />
                            </FormControl>
                            <FormDescription>
                              Sales rep maintaining the account but not earning commissions
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control as any}
                        name="consolidatedBillingFlag"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Consolidated Billing</FormLabel>
                            <div className="flex flex-row items-start space-x-3">
                              <FormControl>
                                <select
                                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                                  value={field.value?.toString() || "0"}
                                  onChange={(e) => field.onChange(parseInt(e.target.value))}
                                >
                                  <option value="0">Disabled</option>
                                  <option value="1">Enabled</option>
                                </select>
                              </FormControl>
                            </div>
                            <FormDescription>
                              Do invoices need to be sent at a parent level to summarize multiple
                              "child" account orders?
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control as any}
                        name="consolidatedParentId"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Consolidated Parent ID</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                placeholder="Parent Account ID"
                                value={field.value?.toString() || ""}
                                onChange={(e) =>
                                  field.onChange(e.target.value ? parseInt(e.target.value) : null)
                                }
                                disabled={form.watch("consolidatedBillingFlag") !== 1}
                              />
                            </FormControl>
                            <FormDescription>
                              ID of parent account for consolidated billing (if applicable)
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>
                </TabsContent>

                {/* Agreement Tab */}
                <TabsContent value="agreement" className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <h3 className="text-lg font-medium">Agreement Details</h3>

                      <FormField
                        control={form.control as any}
                        name="agreementID"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Agreement ID</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                placeholder="Legal agreement ID"
                                value={field.value?.toString() || ""}
                                onChange={(e) =>
                                  field.onChange(e.target.value ? parseInt(e.target.value) : null)
                                }
                              />
                            </FormControl>
                            <FormDescription>
                              Legal agreement ID associated with this account
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control as any}
                        name="term"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Term (months)</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                placeholder="Term length in months"
                                value={field.value?.toString() || ""}
                                onChange={(e) =>
                                  field.onChange(e.target.value ? parseInt(e.target.value) : null)
                                }
                              />
                            </FormControl>
                            <FormDescription>
                              Number of months the agreement is valid for
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control as any}
                        name="unlimitedAutoRenewFlag"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Auto Renewal</FormLabel>
                            <div className="flex flex-row items-start space-x-3">
                              <FormControl>
                                <select
                                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                                  value={field.value?.toString() || "0"}
                                  onChange={(e) => field.onChange(parseInt(e.target.value))}
                                >
                                  <option value="0">Disabled</option>
                                  <option value="1">Enabled</option>
                                </select>
                              </FormControl>
                            </div>
                            <FormDescription>
                              Does the contract automatically renew indefinitely (no terms or end
                              date)?
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="space-y-4">
                      <h3 className="text-lg font-medium">Agreement Dates</h3>

                      <FormField
                        control={form.control as any}
                        name="agreementStartDate"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Start Date</FormLabel>
                            <FormControl>
                              <Input type="date" {...field} value={field.value || ""} />
                            </FormControl>
                            <FormDescription>
                              Start date of the agreement or electronic EULA
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control as any}
                        name="agreementEndDate"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>End Date</FormLabel>
                            <FormControl>
                              <Input
                                type="date"
                                value={field.value || ""}
                                onChange={field.onChange}
                                disabled={form.watch("unlimitedAutoRenewFlag") === 1}
                              />
                            </FormControl>
                            <FormDescription>
                              End date of the agreement (if applicable)
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>
                </TabsContent>

                {/* BPO Products Pricing Tab - Now visible to all users */}
                <TabsContent value="pricing" className="space-y-4">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="text-lg font-medium">Pricing Structures</h3>
                        <p className="text-sm text-muted-foreground">
                          {isAdmin 
                            ? "Configure pricing for BPO products. Choose between standard pricing or set custom special pricing for each product."
                            : "View BPO products pricing. All products are set to Standard Pricing. Contact an admin for special pricing configuration."
                          }
                        </p>
                      </div>
                      {!isAdmin && (
                        <div className="flex items-center gap-2 text-sm text-blue-600 bg-blue-50 px-3 py-2 rounded-md">
                          <AlertCircle className="h-4 w-4" />
                          <span>Read-only view</span>
                        </div>
                      )}
                    </div>

                    {!isAdmin && (
                      <Alert className="bg-blue-50 border-blue-200 text-blue-800">
                        <AlertCircle className="h-4 w-4" />
                        <AlertDescription>
                          <strong>Need special pricing?</strong> Save this customer setup as a draft and notify an admin to configure special pricing rates.
                        </AlertDescription>
                      </Alert>
                    )}

                    {pricingStructures.length === 0 ? (
                      <div className="bg-amber-50 p-4 rounded-md border border-amber-200 text-amber-800">
                        <p>Loading BPO products...</p>
                      </div>
                    ) : (
                      <div className="rounded-md border">
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>Product Name</TableHead>
                              <TableHead>Product ID</TableHead>
                              <TableHead>Pricing Type</TableHead>
                              <TableHead>Price</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {pricingStructures.map((structure) => {
                              const productPricing = getBpoProductPricing(structure.id);
                              const pricingType = isAdmin ? (productPricing?.pricingType || "standard") : "standard";
                              const currentPrice = productPricing?.price || 0;
                              const standardPrice = getStandardPrice(structure.id);
                              
                              return (
                                <TableRow key={structure.id}>
                                  <TableCell className="font-medium">{structure.name}</TableCell>
                                  <TableCell>{structure.id}</TableCell>
                                  <TableCell>
                                    {isAdmin ? (
                                      <select
                                        className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                                        value={pricingType}
                                        onChange={(e) => 
                                          handlePricingTypeChange(structure.id, e.target.value as "standard" | "special")
                                        }
                                      >
                                        <option value="standard">Standard Pricing</option>
                                        <option value="special">Special Pricing</option>
                                      </select>
                                    ) : (
                                      <div className="flex h-10 w-full rounded-md border border-gray-200 bg-gray-50 px-3 py-2 text-sm text-gray-700">
                                        Standard Pricing
                                      </div>
                                    )}
                                  </TableCell>
                                  <TableCell>
                                    <div className="relative">
                                      <DollarSign className="absolute left-2 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                                      {(pricingType === "standard" || !isAdmin) ? (
                                        <div className="pl-7 pr-3 py-2 border border-gray-200 rounded-md bg-gray-50 text-gray-700">
                                          {standardPrice.toFixed(2)}
                                        </div>
                                      ) : (
                                        <FormField
                                          control={form.control as any}
                                          name={`bpoProductsPricing.${pricingStructures.findIndex(p => p.id === structure.id)}.price`}
                                          render={({ field, fieldState }) => (
                                            <FormItem>
                                              <FormControl>
                                                <Input
                                                  type="number"
                                                  step="0.01"
                                                  min="0.01"
                                                  placeholder="0.00"
                                                  className={`pl-7 ${fieldState.error ? 'border-red-500' : ''}`}
                                                  value={currentPrice === null ? "" : currentPrice.toString()}
                                                  onChange={(e) => {
                                                    handlePriceChange(structure.id, e.target.value);
                                                    field.onChange(e.target.value === "" ? null : parseFloat(e.target.value));
                                                  }}
                                                />
                                              </FormControl>
                                              {fieldState.error && (
                                                <p className="text-xs text-red-600 mt-1">
                                                  {fieldState.error.message}
                                                </p>
                                              )}
                                            </FormItem>
                                          )}
                                        />
                                      )}
                                    </div>
                                  </TableCell>
                                </TableRow>
                              );
                            })}
                          </TableBody>
                        </Table>
                      </div>
                    )}
                  </div>
                </TabsContent>
              </Tabs>
            </ScrollArea>

            <DialogFooter className="mt-6">
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsOpen(false)}
                disabled={isCreating}
              >
                Cancel
              </Button>
              
              {isAdmin ? (
                <Button type="submit" disabled={isCreating}>
                  {isCreating ? "Creating..." : "Create Customer"}
                </Button>
              ) : (
                <div className="flex gap-2">
                  <Button 
                    type="button" 
                    variant="outline"
                    onClick={form.handleSubmit(onSaveAsDraft)}
                    disabled={isCreating}
                    className="bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100"
                  >
                    {isCreating ? "Saving..." : "Save as Draft for Admin Review"}
                  </Button>
                  <Button type="submit" disabled={isCreating}>
                    {isCreating ? "Creating..." : "Create Customer (Standard Pricing)"}
                  </Button>
                </div>
              )}
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default CreateCustomerForm;