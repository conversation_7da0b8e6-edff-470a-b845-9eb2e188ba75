import { use<PERSON>tom } from "jotai";
import { useState, useMemo } from "react";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { UserIcon, MousePointerClickIcon, ChevronUp, ChevronDown } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";

import { 
  searchTermAtom,
  searchResultsAtom, 
  customersAtom,
  selectedCustomerAtom,
  isLoadingAtom
} from "@/jotai/customers";

type SortDirection = 'asc' | 'desc' | null;
type SortableColumn = 'company' | 'status' | 'contact';

const CustomerResultsTable = () => {
  const [searchTerm] = useAtom(searchTermAtom);
  const [searchResults] = useAtom(searchResultsAtom);
  const [allCustomers] = useAtom(customersAtom);
  const [selectedCustomer, setSelectedCustomer] = useAtom(selectedCustomerAtom);
  const [isLoading] = useAtom(isLoadingAtom);

  // Sorting state
  const [sortColumn, setSortColumn] = useState<SortableColumn | null>(null);
  const [sortDirection, setSortDirection] = useState<SortDirection>(null);
  
  // Active filter state
  const [showActiveOnly, setShowActiveOnly] = useState(true);

  // Determine which customers to display
  const baseCustomers = searchTerm.trim() ? searchResults : allCustomers;

  // Sort and filter the customers based on current settings
  const displayCustomers = useMemo(() => {
    let filteredCustomers = baseCustomers;

    // Apply active filter if enabled
    if (showActiveOnly) {
      filteredCustomers = filteredCustomers.filter(customer => customer.isActiveFlag);
    }

    if (!sortColumn || !sortDirection) {
      return filteredCustomers;
    }

    return [...filteredCustomers].sort((a, b) => {
      let aValue: string;
      let bValue: string;

      switch (sortColumn) {
        case 'company':
          aValue = a.company?.toLowerCase() || '';
          bValue = b.company?.toLowerCase() || '';
          break;
        case 'status':
          aValue = a.isActiveFlag ? 'active' : 'inactive';
          bValue = b.isActiveFlag ? 'active' : 'inactive';
          break;
        case 'contact':
          aValue = a.contactName?.toLowerCase() || '';
          bValue = b.contactName?.toLowerCase() || '';
          break;
        default:
          return 0;
      }

      if (aValue < bValue) {
        return sortDirection === 'asc' ? -1 : 1;
      }
      if (aValue > bValue) {
        return sortDirection === 'asc' ? 1 : -1;
      }
      return 0;
    });
  }, [baseCustomers, sortColumn, sortDirection, showActiveOnly]);

  const handleColumnClick = (column: SortableColumn) => {
    if (sortColumn === column) {
      // Toggle direction or clear sort
      if (sortDirection === 'asc') {
        setSortDirection('desc');
      } else if (sortDirection === 'desc') {
        setSortColumn(null);
        setSortDirection(null);
      } else {
        setSortDirection('asc');
      }
    } else {
      // Set new column with ascending sort
      setSortColumn(column);
      setSortDirection('asc');
    }
  };

  const getSortIcon = (column: SortableColumn) => {
    if (sortColumn !== column) {
      return null;
    }

    if (sortDirection === 'asc') {
      return <ChevronUp className="h-4 w-4 ml-1 inline" />;
    } else if (sortDirection === 'desc') {
      return <ChevronDown className="h-4 w-4 ml-1 inline" />;
    }

    return null;
  };

  const handleRowClick = (customerId: number | null) => {
    const customer = displayCustomers.find(c => c.id === customerId) || null;
    setSelectedCustomer(customer);
  };

  // If loading, show a loading state
  if (isLoading) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="flex justify-center items-center p-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // If no customers at all
  if (allCustomers.length === 0) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col items-center justify-center text-center p-6">
            <UserIcon className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-xl font-semibold">No customers found</h3>
            <p className="text-muted-foreground mt-2">
              There are no customers in the system yet.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // If search has been performed but no results found
  if (searchTerm.trim() && searchResults.length === 0) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col items-center justify-center text-center p-6">
            <UserIcon className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-xl font-semibold">No customers found</h3>
            <p className="text-muted-foreground mt-2">
              No customers match the search term "{searchTerm}".
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Show the table with all customers or search results
  return (
    <Card className="text-sm">
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle>
              {searchTerm.trim() 
                ? 'Search Results' 
                : showActiveOnly 
                  ? 'Active Customers'
                  : 'All Customers'
              }
            </CardTitle>
            <CardDescription>
              {searchTerm.trim() 
                ? `Found ${displayCustomers.length} customer${displayCustomers.length !== 1 ? 's' : ''}`
                : `Showing ${displayCustomers.length} customer${displayCustomers.length !== 1 ? 's' : ''}`
              }
              {sortColumn && sortDirection && (
                <span className="ml-2">• Sorted by {sortColumn} ({sortDirection === 'asc' ? 'A-Z' : 'Z-A'})</span>
              )}
              {showActiveOnly && (
                <span className="ml-2">• Active only</span>
              )}
            </CardDescription>
          </div>
          <div className="flex items-center space-x-2">
            <Switch
              id="active-filter"
              checked={showActiveOnly}
              onCheckedChange={setShowActiveOnly}
            />
            <Label htmlFor="active-filter">Show Active Only</Label>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {!selectedCustomer && (
          <Alert className="mb-4 bg-blue-50 text-blue-800 border-blue-200">
            <MousePointerClickIcon className="h-4 w-4 mr-2" />
            <AlertDescription>
              Click on a row to view detailed customer information. Click column headers to sort.
            </AlertDescription>
          </Alert>
        )}
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>ID</TableHead>
                <TableHead>Account #</TableHead>
                <TableHead 
                  className="cursor-pointer hover:bg-muted select-none"
                  onClick={() => handleColumnClick('company')}
                >
                  <div className="flex items-center">
                    Company
                    {getSortIcon('company')}
                  </div>
                </TableHead>
                <TableHead 
                  className="cursor-pointer hover:bg-muted select-none"
                  onClick={() => handleColumnClick('status')}
                >
                  <div className="flex items-center">
                    Status
                    {getSortIcon('status')}
                  </div>
                </TableHead>
                <TableHead 
                  className="cursor-pointer hover:bg-muted select-none"
                  onClick={() => handleColumnClick('contact')}
                >
                  <div className="flex items-center">
                    Contact
                    {getSortIcon('contact')}
                  </div>
                </TableHead>
                <TableHead>Email</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {displayCustomers.map((customer) => (
                <TableRow 
                  key={customer.id} 
                  onClick={() => handleRowClick(customer.id)}
                  className={`cursor-pointer hover:bg-muted ${
                    selectedCustomer?.id === customer.id ? "bg-muted" : ""
                  }`}
                >
                  <TableCell>{customer.id}</TableCell>
                  <TableCell>{customer.sourceAcctNo}</TableCell>
                  <TableCell className="font-medium">{customer.company}</TableCell>
                  <TableCell>
                    {customer.isActiveFlag ? (
                      <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200 hover:bg-green-50">Active</Badge>
                    ) : (
                      <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200 hover:bg-red-50">Inactive</Badge>
                    )}
                  </TableCell>
                  <TableCell>{customer.contactName || "N/A"}</TableCell>
                  <TableCell className="max-w-[200px] truncate">{customer.email}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </CardContent>
      <CardFooter className="text-sm text-muted-foreground pt-0">
        {displayCustomers.length > 0 && !selectedCustomer && (
          <div className="flex items-center">
            <MousePointerClickIcon className="h-3 w-3 mr-1" />
            <span>Select a customer to see detailed information</span>
          </div>
        )}
      </CardFooter>
    </Card>
  );
};

export default CustomerResultsTable;