import { use<PERSON><PERSON> } from "jotai";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Ta<PERSON>,
  <PERSON><PERSON>Content,
  <PERSON><PERSON>List,
  TabsTrigger,
} from "@/components/ui/tabs";
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  UserIcon, 
  Building2Icon, 
  PhoneIcon, 
  MailIcon, 
  MapPinIcon, 
  CalendarIcon, 
  CheckCircleIcon, 
  XCircleIcon,
  EditIcon,
  Trash2Icon,
  DollarSignIcon,
  TagIcon,
  InfoIcon
} from "lucide-react";

import { 
  selectedCustomerAtom, 
  initEditFormAtom, 
  isDeleteConfirmOpenAtom 
} from "@/jotai/customers";
import { getAllPricingStructures } from "@/services/data";
import { PricingStructure } from "./types";
import { useEffect, useState } from "react";

import DeleteConfirmationModal from "./DeleteConfirmationModal";
import EditCustomerModal from "./EditCustomerForm";

const CustomerDetails = () => {
  const [selectedCustomer] = useAtom(selectedCustomerAtom);
  const [, initEditForm] = useAtom(initEditFormAtom);
  const [, setIsDeleteConfirmOpen] = useAtom(isDeleteConfirmOpenAtom);
  const [pricingStructures, setPricingStructures] = useState<PricingStructure[]>([]);

  useEffect(() => {
    const fetchPricingStructures = async () => {
      try {
        const structures = await getAllPricingStructures();
        setPricingStructures(structures);
      } catch (error) {
        console.error("Failed to fetch pricing structures:", error);
      }
    };

    fetchPricingStructures();
  }, []);

  if (!selectedCustomer) {
    return null;
  }

  const formatDate = (dateString: string | null) => {
    if (!dateString) return "N/A";
    return new Date(dateString).toLocaleDateString();
  };

  const handleEditClick = () => {
    initEditForm();
  };
  
  const handleDeleteClick = () => {
    setIsDeleteConfirmOpen(true);
  };

  const formatPrice = (price: string | number | null) => {
    if (price === null || price === undefined) return "N/A";
    const numPrice = typeof price === "string" ? parseFloat(price) : price;
    return `$${numPrice.toFixed(2)}`;
  };

  return (
    <>
      <Card className="text-sm">
        <CardHeader>
          <div className="flex justify-between items-start">
            <div>
              <CardTitle>{selectedCustomer.company}</CardTitle>
              <CardDescription>
                Account #{selectedCustomer.sourceAcctNo} | ID: {selectedCustomer.id}
              </CardDescription>
            </div>
            <div className="flex items-center space-x-2">
              <Badge 
                variant={selectedCustomer.isActiveFlag ? "outline" : "destructive"}
                className={selectedCustomer.isActiveFlag ? "bg-green-50 text-green-700 border-green-200" : ""}
              >
                {selectedCustomer.isActiveFlag ? "Active" : "Inactive"}
              </Badge>
              <Button 
                variant="outline" 
                size="sm" 
                className="h-8 px-2" 
                onClick={handleEditClick}
              >
                <EditIcon className="h-4 w-4 mr-1" /> Edit
              </Button>
              <Button 
                variant="outline" 
                size="sm" 
                className="h-8 px-2 border-red-200 hover:bg-red-50 hover:text-red-600" 
                onClick={handleDeleteClick}
              >
                <Trash2Icon className="h-4 w-4 mr-1" /> Deactive
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="details" className="w-full">
            <TabsList className="grid grid-cols-4 w-full md:w-[500px]">
              <TabsTrigger value="details">Details</TabsTrigger>
              <TabsTrigger value="contact">Contact</TabsTrigger>
              <TabsTrigger value="agreement">Agreement</TabsTrigger>
              <TabsTrigger value="pricing">Pricing</TabsTrigger>
            </TabsList>
            
            <TabsContent value="details" className="pt-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-4">
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">Billing Address</h3>
                    <div className="flex items-start">
                      <MapPinIcon className="h-5 w-5 mr-2 text-muted-foreground shrink-0 mt-0.5" />
                      <div>
                        <p>{selectedCustomer.bAddress1}</p>
                        {selectedCustomer.bAddress2 && <p>{selectedCustomer.bAddress2}</p>}
                        <p>
                          {selectedCustomer.bCity}, {selectedCustomer.bState} {selectedCustomer.bZip}
                        </p>
                        <p>{selectedCustomer.county} County</p>
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">Industry & Classification</h3>
                    <p>Industry ID: {selectedCustomer.industryId || "N/A"}</p>
                    <p>Classification: {selectedCustomer.classification ? "Yes" : "No"}</p>
                  </div>
                </div>
                
                <div className="space-y-4">
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">Billing Settings</h3>
                    <div className="grid grid-cols-2 gap-2">
                      <div className="flex items-center">
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <div className="flex items-center">
                                {selectedCustomer.isBillable ? (
                                  <CheckCircleIcon className="h-4 w-4 mr-1 text-green-500" />
                                ) : (
                                  <XCircleIcon className="h-4 w-4 mr-1 text-red-500" />
                                )}
                                <span>Billable</span>
                              </div>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Customer can be billed</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </div>
                      
                      <div className="flex items-center">
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <div className="flex items-center">
                                {selectedCustomer.isTaxableFlag ? (
                                  <CheckCircleIcon className="h-4 w-4 mr-1 text-green-500" />
                                ) : (
                                  <XCircleIcon className="h-4 w-4 mr-1 text-red-500" />
                                )}
                                <span>Taxable</span>
                              </div>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Customer is subject to tax</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </div>
                      
                      <div className="flex items-center">
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <div className="flex items-center">
                                {selectedCustomer.creditCardFlag ? (
                                  <CheckCircleIcon className="h-4 w-4 mr-1 text-green-500" />
                                ) : (
                                  <XCircleIcon className="h-4 w-4 mr-1 text-red-500" />
                                )}
                                <span>Credit Card</span>
                              </div>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Customer uses credit card for payment</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </div>
                      
                      <div className="flex items-center">
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <div className="flex items-center">
                                {selectedCustomer.statement === true ? (
                                  <CheckCircleIcon className="h-4 w-4 mr-1 text-green-500" />
                                ) : (
                                  <XCircleIcon className="h-4 w-4 mr-1 text-red-500" />
                                )}
                                <span>Statement</span>
                              </div>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Customer receives statements</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">Consolidated Billing</h3>
                    <p>
                      {selectedCustomer.consolidatedBillingFlag === 1 ? (
                        <>
                          <CheckCircleIcon className="h-4 w-4 inline mr-1 text-green-500" />
                          <span>Enabled</span>
                        </>
                      ) : (
                        <>
                          <XCircleIcon className="h-4 w-4 inline mr-1 text-red-500" />
                          <span>Disabled</span>
                        </>
                      )}
                    </p>
                    {selectedCustomer.consolidatedParentId && (
                      <p>Parent ID: {selectedCustomer.consolidatedParentId}</p>
                    )}
                  </div>
                  
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">Oracle Sales Representatives</h3>
                    <p>Primary: {selectedCustomer.oracleSalesPersonId || "N/A"}</p>
                    <p>Secondary: {selectedCustomer.secondaryOracleSalesPersonId || "N/A"}</p>
                  </div>
                </div>
              </div>
              
              {selectedCustomer.contractSummary && (
                <div className="mt-4">
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">Contract Summary</h3>
                  <p>{selectedCustomer.contractSummary}</p>
                </div>
              )}
            </TabsContent>
            
            <TabsContent value="contact" className="pt-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">Primary Contact</h3>
                    <div className="flex items-center">
                      <UserIcon className="h-5 w-5 mr-2 text-muted-foreground" />
                      <span>{selectedCustomer.contactName || "Not specified"}</span>
                    </div>
                    {selectedCustomer.attn && (
                      <div className="flex items-center mt-1">
                        <span className="text-sm text-muted-foreground ml-7">Attention: {selectedCustomer.attn}</span>
                      </div>
                    )}
                  </div>
                  
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">Company</h3>
                    <div className="flex items-center">
                      <Building2Icon className="h-5 w-5 mr-2 text-muted-foreground" />
                      <span>{selectedCustomer.company}</span>
                    </div>
                  </div>
                </div>
                
                <div className="space-y-4">
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">Contact Methods</h3>
                    <div className="flex items-center mb-2">
                      <PhoneIcon className="h-5 w-5 mr-2 text-muted-foreground" />
                      <span>{selectedCustomer.phone}</span>
                      {selectedCustomer.extn && (
                        <span className="text-sm text-muted-foreground ml-1">ext. {selectedCustomer.extn}</span>
                      )}
                    </div>
                    
                    <div className="flex items-center mb-2">
                      <MailIcon className="h-5 w-5 mr-2 text-muted-foreground" />
                      <a 
                        href={`mailto:${selectedCustomer.email}`} 
                        className="text-blue-600 hover:underline"
                      >
                        {selectedCustomer.email}
                      </a>
                    </div>
                    
                    {selectedCustomer.fax && (
                      <div className="flex items-start">
                        <span className="text-sm mr-2">Fax:</span>
                        <span>{selectedCustomer.fax}</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </TabsContent>
            
            <TabsContent value="agreement" className="pt-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">Agreement Details</h3>
                    <p>Agreement ID: {selectedCustomer.agreementId || "N/A"}</p>
                    <p>Term: {selectedCustomer.term ? `${selectedCustomer.term} months` : "N/A"}</p>
                    <p>
                      Auto Renew: {selectedCustomer.unlimitedAutoRenewFlag === 1 ? (
                        <span className="text-green-600">Yes</span>
                      ) : (
                        <span className="text-red-600">No</span>
                      )}
                    </p>
                  </div>
                </div>
                
                <div className="space-y-4">
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">Agreement Dates</h3>
                    <div className="flex items-center mb-2">
                      <CalendarIcon className="h-5 w-5 mr-2 text-muted-foreground" />
                      <div>
                        <p>
                          <span className="text-sm text-muted-foreground">Start: </span>
                          {formatDate(selectedCustomer.agreementStartDate)}
                        </p>
                        <p>
                          <span className="text-sm text-muted-foreground">End: </span>
                          {formatDate(selectedCustomer.agreementEndDate)}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="pricing" className="pt-4">
              <div className="space-y-4">
                <div className="flex items-center mb-4">
                  <DollarSignIcon className="h-5 w-5 mr-2 text-muted-foreground" />
                  <h3 className="text-lg font-medium">Pricing Structures</h3>
                </div>

                {selectedCustomer.pricingStructures && selectedCustomer.pricingStructures.length > 0 ? (
                  <div className="space-y-3">
                    {Object.values(
                      selectedCustomer.pricingStructures
                        .filter((structure): structure is NonNullable<typeof structure> => 
                          structure !== null && structure !== undefined && 
                          typeof structure === 'object' && 
                          'pricingStructureId' in structure
                        )
                        .reduce((acc, structure) => {
                          const existing = acc[structure.pricingStructureId];
                          if (!existing || 
                              (structure.id && existing.id && 
                               structure.id > existing.id)) {
                            acc[structure.pricingStructureId] = structure;
                          }
                          return acc;
                        }, {} as Record<number, NonNullable<typeof selectedCustomer.pricingStructures[0]>>)
                    ).map((customerStructure) => {
                      const fullStructure = pricingStructures.find(
                        ps => ps.id === customerStructure.pricingStructureId
                      );

                      if (!fullStructure) return null;

                      return (
                        <Card key={`${fullStructure.id}-${customerStructure.id}`} className="p-4">
                          <div className="flex items-start justify-between">
                            <div className="space-y-2 flex-1">
                              <div className="flex items-center gap-2">
                                <TagIcon className="h-4 w-4 text-muted-foreground" />
                                <span className="font-medium">{fullStructure.name}</span>
                                <TooltipProvider>
                                  <Tooltip>
                                    <TooltipTrigger asChild>
                                      <div className="inline-flex">
                                        <InfoIcon className="h-4 w-4 text-muted-foreground cursor-help" />
                                      </div>
                                    </TooltipTrigger>
                                    <TooltipContent>
                                      <p>Product Code: {fullStructure.productCode}</p>
                                    </TooltipContent>
                                  </Tooltip>
                                </TooltipProvider>
                              </div>
                              
                              <div className="flex items-center gap-2">
                                {customerStructure.specialPricing ? (
                                  <>
                                    <Badge variant="secondary" className="bg-orange-50 text-orange-700 border-orange-200">
                                      Special Price: {formatPrice(customerStructure.specialPricing)}
                                    </Badge>
                                  </>
                                ) : (
                                  <>
                                    <Badge variant="outline" className="bg-gray-50 text-gray-700">
                                      Standard Price: {formatPrice(fullStructure.standardPricing)}
                                    </Badge>
                                  </>
                                )}
                              </div>
                            </div>
                          </div>
                        </Card>
                      );
                    })}
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <DollarSignIcon className="h-12 w-12 mx-auto mb-3 opacity-50" />
                    <p>No pricing structures assigned to this customer</p>
                  </div>
                )}
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
      <EditCustomerModal />
      <DeleteConfirmationModal />
    </>
  );
};

export default CustomerDetails;