import { use<PERSON>tom } from "jotai";
import { useState, useEffect } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import { Loader2 } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  isEditModalOpenAtom, 
  editCustomerFormAtom, 
  updateCustomerAtom, 
  isUpdatingCustomerAtom, 
  updateCustomerErrorAtom 
} from "@/jotai/customers";
import { ClientCustomer, PricingStructure, PricingStructureSelection } from "@/components/Customer/types";
import { getAllPricingStructures } from "@/services/data";

const EditCustomerModal = () => {
  const [isOpen, setIsOpen] = useAtom(isEditModalOpenAtom);
  const [customerForm, setCustomerForm] = useAtom(editCustomerFormAtom);
  const [, updateCustomer] = useAtom(updateCustomerAtom);
  const [isUpdating] = useAtom(isUpdatingCustomerAtom);
  const [updateError] = useAtom(updateCustomerErrorAtom);
  const [activeTab, setActiveTab] = useState("details");
  
  // State for pricing structures
  const [pricingStructures, setPricingStructures] = useState<PricingStructure[]>([]);
  const [selectedPricingStructures, setSelectedPricingStructures] = useState<Map<number, PricingStructureSelection>>(new Map());
  const [validationErrors, setValidationErrors] = useState<Map<number, string>>(new Map());

  // Fetch pricing structures when the modal opens
  useEffect(() => {
    if (isOpen) {
      const fetchPricingStructures = async () => {
        try {
          const structures = await getAllPricingStructures();
          setPricingStructures(structures);
        } catch (error) {
          console.error("Failed to fetch pricing structures:", error);
        }
      };
      fetchPricingStructures();
    }
  }, [isOpen]);

  // Initialize selected pricing structures from customer data
  useEffect(() => {
    if (customerForm?.pricingStructures) {
      const newMap = new Map<number, PricingStructureSelection>();
      
      customerForm.pricingStructures.forEach((ps) => {
        newMap.set(ps.pricingStructureId, {
          pricingStructureId: ps.pricingStructureId,
          specialPricing: ps.specialPricing
        });
      });
      
      setSelectedPricingStructures(newMap);
    }
  }, [customerForm]);

  // Validate special pricing input
  const validateSpecialPricing = (value: string): string | null => {
    if (value.trim() === '') return null; // Empty is valid (no special pricing)
    
    const numValue = parseFloat(value);
    if (isNaN(numValue)) {
      return "Please enter a valid number";
    }
    
    if (numValue < 0) {
      return "Value must be non-negative";
    }
    
    if (numValue > 999999.99) {
      return "Value must be less than 1,000,000";
    }
    
    if (!/^\d*\.?\d{0,2}$/.test(value)) {
      return "Maximum 2 decimal places allowed";
    }
    
    return null;
  };

  const handleClose = () => {
    setIsOpen(false);
    setValidationErrors(new Map());
  };

  const handleFormChange = (field: keyof ClientCustomer, value: any) => {
    if (!customerForm) return;
    
    setCustomerForm({
      ...customerForm,
      [field]: value,
    });
  };

  const handleCheckboxChange = (field: keyof ClientCustomer) => {
    if (!customerForm) return;
    
    setCustomerForm({
      ...customerForm,
      [field]: !customerForm[field],
    });
  };

  // Toggle pricing structure selection
  const togglePricingStructure = (id: number, checked: boolean) => {
    const newMap = new Map(selectedPricingStructures);
    
    if (checked) {
      newMap.set(id, {
        pricingStructureId: id,
        specialPricing: null
      });
    } else {
      newMap.delete(id);
    }
    
    setSelectedPricingStructures(newMap);
    updatePricingStructuresField(newMap);
  };

  // Handle special pricing input change
  const handleSpecialPricingChange = (id: number, value: string) => {
    const newMap = new Map(selectedPricingStructures);
    const existing = newMap.get(id);
    const newErrorsMap = new Map(validationErrors);
    
    if (existing) {
      const error = validateSpecialPricing(value);
      if (error) {
        newErrorsMap.set(id, error);
      } else {
        newErrorsMap.delete(id);
        newMap.set(id, {
          ...existing,
          specialPricing: value.trim() === '' ? null : parseFloat(value)
        });
      }
      
      setValidationErrors(newErrorsMap);
      setSelectedPricingStructures(newMap);
      updatePricingStructuresField(newMap);
    }
  };

  // Update the form's pricingStructures field
  const updatePricingStructuresField = (selectionsMap: Map<number, PricingStructureSelection>) => {
    if (!customerForm) return;

    const pricingStructuresArray = Array.from(selectionsMap.values());
    setCustomerForm({
      ...customerForm,
      pricingStructures: pricingStructuresArray
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Check for any validation errors before submitting
    if (validationErrors.size > 0) {
      return;
    }
    
    updateCustomer();
  };

  if (!customerForm) return null;

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogContent className="min-w-[800px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit Customer</DialogTitle>
          <DialogDescription>
            Update customer information for {customerForm.company}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit}>
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid grid-cols-4 w-full md:w-[500px]">
              <TabsTrigger value="details">Details</TabsTrigger>
              <TabsTrigger value="contact">Contact</TabsTrigger>
              <TabsTrigger value="agreement">Agreement</TabsTrigger>
              <TabsTrigger value="pricing">Pricing</TabsTrigger>
            </TabsList>

            <TabsContent value="details" className="space-y-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="company">Company Name</Label>
                  <Input
                    id="company"
                    value={customerForm.company || ""}
                    onChange={(e) => handleFormChange("company", e.target.value)}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="sourceAcctNo">Account Number</Label>
                  <Input
                    id="sourceAcctNo"
                    value={customerForm.sourceAcctNo || ""}
                    onChange={(e) => handleFormChange("sourceAcctNo", e.target.value)}
                    disabled
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="bAddress1">Billing Address 1</Label>
                  <Input
                    id="bAddress1"
                    value={customerForm.bAddress1 || ""}
                    onChange={(e) => handleFormChange("bAddress1", e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="bAddress2">Billing Address 2</Label>
                  <Input
                    id="bAddress2"
                    value={customerForm.bAddress2 || ""}
                    onChange={(e) => handleFormChange("bAddress2", e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="bCity">City</Label>
                  <Input
                    id="bCity"
                    value={customerForm.bCity || ""}
                    onChange={(e) => handleFormChange("bCity", e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="bState">State</Label>
                  <Input
                    id="bState"
                    value={customerForm.bState || ""}
                    onChange={(e) => handleFormChange("bState", e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="bZip">Zip Code</Label>
                  <Input
                    id="bZip"
                    value={customerForm.bZip || ""}
                    onChange={(e) => handleFormChange("bZip", e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="county">County</Label>
                  <Input
                    id="county"
                    value={customerForm.county || ""}
                    onChange={(e) => handleFormChange("county", e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="industryId">Industry ID</Label>
                  <Input
                    id="industryId"
                    value={customerForm.industryId || ""}
                    onChange={(e) => handleFormChange("industryId", e.target.value)}
                  />
                </div>

                <div className="col-span-2 flex items-start space-x-6">
                  <div className="flex items-center space-x-2">
                    <Checkbox 
                      id="isActiveFlag" 
                      checked={customerForm.isActiveFlag || false} 
                      onCheckedChange={() => handleCheckboxChange("isActiveFlag")} 
                    />
                    <Label htmlFor="isActiveFlag">Active</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox 
                      id="isBillable" 
                      checked={customerForm.isBillable || false} 
                      onCheckedChange={() => handleCheckboxChange("isBillable")} 
                    />
                    <Label htmlFor="isBillable">Billable</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox 
                      id="isTaxableFlag" 
                      checked={customerForm.isTaxableFlag || false} 
                      onCheckedChange={() => handleCheckboxChange("isTaxableFlag")} 
                    />
                    <Label htmlFor="isTaxableFlag">Taxable</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox 
                      id="creditCardFlag" 
                      checked={customerForm.creditCardFlag || false} 
                      onCheckedChange={() => handleCheckboxChange("creditCardFlag")} 
                    />
                    <Label htmlFor="creditCardFlag">Credit Card</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox 
                      id="statement" 
                      checked={customerForm.statement || false} 
                      onCheckedChange={() => handleCheckboxChange("statement")} 
                    />
                    <Label htmlFor="statement">Statement</Label>
                  </div>
                </div>

                <div className="col-span-2 space-y-2">
                  <Label htmlFor="contractSummary">Contract Summary</Label>
                  <Textarea
                    id="contractSummary"
                    value={customerForm.contractSummary || ""}
                    onChange={(e) => handleFormChange("contractSummary", e.target.value)}
                    rows={3}
                  />
                </div>
              </div>
            </TabsContent>

            <TabsContent value="contact" className="space-y-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="contactName">Contact Name</Label>
                  <Input
                    id="contactName"
                    value={customerForm.contactName || ""}
                    onChange={(e) => handleFormChange("contactName", e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="attn">Attention</Label>
                  <Input
                    id="attn"
                    value={customerForm.attn || ""}
                    onChange={(e) => handleFormChange("attn", e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="phone">Phone</Label>
                  <Input
                    id="phone"
                    value={customerForm.phone || ""}
                    onChange={(e) => handleFormChange("phone", e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="extn">Extension</Label>
                  <Input
                    id="extn"
                    value={customerForm.extn || ""}
                    onChange={(e) => handleFormChange("extn", e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    value={customerForm.email || ""}
                    onChange={(e) => handleFormChange("email", e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="fax">Fax</Label>
                  <Input
                    id="fax"
                    value={customerForm.fax || ""}
                    onChange={(e) => handleFormChange("fax", e.target.value)}
                  />
                </div>
              </div>
            </TabsContent>

            <TabsContent value="agreement" className="space-y-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="agreementId">Agreement ID</Label>
                  <Input
                    id="agreementId"
                    value={customerForm.agreementId || ""}
                    onChange={(e) => handleFormChange("agreementId", e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="term">Term (months)</Label>
                  <Input
                    id="term"
                    type="number"
                    value={customerForm.term || ""}
                    onChange={(e) => handleFormChange("term", parseInt(e.target.value) || "")}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="agreementStartDate">Start Date</Label>
                  <Input
                    id="agreementStartDate"
                    type="date"
                    value={customerForm.agreementStartDate ? new Date(customerForm.agreementStartDate).toISOString().split('T')[0] : ""}
                    onChange={(e) => handleFormChange("agreementStartDate", e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="agreementEndDate">End Date</Label>
                  <Input
                    id="agreementEndDate"
                    type="date"
                    value={customerForm.agreementEndDate ? new Date(customerForm.agreementEndDate).toISOString().split('T')[0] : ""}
                    onChange={(e) => handleFormChange("agreementEndDate", e.target.value)}
                  />
                </div>

                <div className="col-span-2 flex items-center space-x-2">
                  <Checkbox 
                    id="unlimitedAutoRenewFlag" 
                    checked={customerForm.unlimitedAutoRenewFlag === 1} 
                    onCheckedChange={(checked) => 
                      handleFormChange("unlimitedAutoRenewFlag", checked ? 1 : 0)
                    } 
                  />
                  <Label htmlFor="unlimitedAutoRenewFlag">Auto Renew</Label>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="pricing" className="space-y-4 py-4">
              <div className="space-y-4">
                <div>
                  <h3 className="text-lg font-medium">Pricing Structures</h3>
                  <p className="text-sm text-muted-foreground">
                    Select pricing structures and optionally set special pricing for this customer.
                  </p>
                </div>

                {pricingStructures.length === 0 ? (
                  <Alert>
                    <AlertDescription>
                      Loading pricing structures...
                    </AlertDescription>
                  </Alert>
                ) : (
                  <div className="space-y-4">
                    {pricingStructures.map((structure) => {
                      const selection = selectedPricingStructures.get(structure.id);
                      const validationError = validationErrors.get(structure.id);

                      return (
                        <div 
                          key={structure.id} 
                          className="border rounded-md p-4 space-y-3"
                        >
                          <div className="flex items-start">
                            <Checkbox
                              id={`pricing-${structure.id}`}
                              checked={!!selection}
                              onCheckedChange={(checked) => 
                                togglePricingStructure(structure.id, checked === true)
                              }
                            />
                            <div className="ml-3 flex-1">
                              <Label 
                                htmlFor={`pricing-${structure.id}`} 
                                className="font-medium cursor-pointer"
                              >
                                {structure.name}
                              </Label>
                              <p className="text-sm text-muted-foreground">
                                Product Code: {structure.productCode} | Standard Price: ${parseFloat(structure.standardPricing || "0").toFixed(2)}
                              </p>
                            </div>
                          </div>

                          {selection && (
                            <div className="ml-7 space-y-2">
                              <Label htmlFor={`special-pricing-${structure.id}`} className="text-sm">
                                Special Price (optional)
                              </Label>
                              <Input
                                id={`special-pricing-${structure.id}`}
                                type="number"
                                step="0.01"
                                min="0"
                                max="999999.99"
                                placeholder="Enter special price"
                                value={selection.specialPricing ?? ''}
                                onChange={(e) => handleSpecialPricingChange(structure.id, e.target.value)}
                                className={validationError ? "border-red-500" : ""}
                              />
                              {validationError && (
                                <p className="text-red-500 text-sm">{validationError}</p>
                              )}
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </div>
                )}
              </div>
            </TabsContent>
          </Tabs>

          {updateError && (
            <Alert variant="destructive" className="mt-4">
              <AlertDescription>{updateError}</AlertDescription>
            </Alert>
          )}

          <DialogFooter className="mt-6">
            <Button type="button" variant="outline" onClick={handleClose} disabled={isUpdating}>
              Cancel
            </Button>
            <Button type="submit" disabled={isUpdating || validationErrors.size > 0}>
              {isUpdating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Updating...
                </>
              ) : (
                "Save Changes"
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default EditCustomerModal;