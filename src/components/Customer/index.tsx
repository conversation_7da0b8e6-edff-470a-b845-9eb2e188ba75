// src/components/CustomerSearch/index.tsx
import { useAtom } from "jotai";
import CustomerSearchBar from "./SearchBar";
import CustomerResultsTable from "./Table";
import CustomerDetails from "./Details";
import CreateCustomerForm from "./CreateForm";
import { selectedCustomerAtom, searchResultsAtom } from "@/jotai/customers";

const CustomerSearch = () => {
  const [selectedCustomer] = useAtom(selectedCustomerAtom);
  const [searchResults] = useAtom(searchResultsAtom);

  return (
    <div className="space-y-6 container mx-auto py-6 px-4">
      <h1 className="text-2xl font-bold tracking-tight">Customer Management</h1>
      <CustomerSearchBar />
      <CustomerResultsTable />
      
      {/* Only show details if a customer is selected */}
      {selectedCustomer && <CustomerDetails />}
      
      {/* Additional guidance when results exist but no customer is selected */}
      {searchResults.length > 0 && !selectedCustomer && (
        <div className="flex flex-col items-center justify-center text-center p-12 bg-gray-50 border border-dashed border-gray-200 rounded-lg">
          <svg 
            xmlns="http://www.w3.org/2000/svg" 
            className="h-12 w-12 text-muted-foreground mb-4" 
            fill="none" 
            viewBox="0 0 24 24" 
            stroke="currentColor"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={1.5} 
              d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122" 
            />
          </svg>
          <h3 className="text-lg font-medium mb-2">View Customer Details</h3>
          <p className="text-muted-foreground max-w-md">
            Click on any customer in the table above to view their detailed information including contact information, billing settings, and agreement details.
          </p>
        </div>
      )}
      
      {/* Customer Creation Modal (always included but only shown when open) */}
      <CreateCustomerForm />
    </div>
  );
};

export default CustomerSearch;