import { useTheme } from "next-themes";
import { <PERSON><PERSON><PERSON>, Toolt<PERSON><PERSON>ontent, TooltipProvider, TooltipTrigger } from "./tooltip";
import { 
  getConfidenceLevel, 
  getConfidenceLevelColor, 
  getConfidenceLevelBg,
  getConfidenceLevelDescription 
} from "@/lib/utils";
import { Badge } from "./badge";
import { cn } from "@/lib/utils";

interface ConfidenceScoreProps {
  score: number | null;
  showTooltip?: boolean;
  variant?: "badge" | "text" | "box";
  size?: "sm" | "md" | "lg";
  className?: string;
}

export function ConfidenceScore({ 
  score, 
  showTooltip = true, 
  variant = "box",
  size = "md",
  className = "" 
}: ConfidenceScoreProps) {
  const { resolvedTheme } = useTheme();
  const isDarkMode = resolvedTheme === "dark";
  const level = getConfidenceLevel(score);
  const colorClass = getConfidenceLevelColor(level, isDarkMode);
  const bgClass = getConfidenceLevelBg(level, isDarkMode);

  // Update the size classes to be more compact
  const sizeClasses = {
    sm: "text-xs px-1 py-0.5",
    md: "text-sm px-1.5 py-0.5",
    lg: "text-sm px-2 py-1"
  };

  const textContent = score !== null ? `${score}%` : 'N/A';

  const content = (
    <span className={cn(
      colorClass,
      "font-medium",
      className
    )}>
      {textContent}
    </span>
  );

  const badgeContent = (
    <Badge 
      variant="outline" 
      className={cn(
        colorClass,
        bgClass,
        "font-medium border-2",
        sizeClasses[size],
        className
      )}
    >
      {textContent}
    </Badge>
  );

  const boxContent = (
    <span 
      className={cn(
        colorClass,
        bgClass,
        "inline-flex items-center justify-center rounded-md font-medium border-2",
        {
          "border-green-200 dark:border-green-800": level === "high",
          "border-yellow-200 dark:border-yellow-800": level === "medium",
          "border-red-200 dark:border-red-800": level === "low",
          "border-gray-200 dark:border-gray-800": level === "na"
        },
        sizeClasses[size],
        className
      )}
    >
      {textContent}
    </span>
  );

  const displayContent = {
    text: content,
    badge: badgeContent,
    box: boxContent
  }[variant];

  if (!showTooltip) {
    return displayContent;
  }

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          {displayContent}
        </TooltipTrigger>
        <TooltipContent 
          className={cn(
            bgClass,
            "font-medium border-2",
            {
              "border-green-200 dark:border-green-800": level === "high",
              "border-yellow-200 dark:border-yellow-800": level === "medium",
              "border-red-200 dark:border-red-800": level === "low",
              "border-gray-200 dark:border-gray-800": level === "na"
            }
          )}
        >
          <p className={colorClass}>{getConfidenceLevelDescription(level)}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
} 