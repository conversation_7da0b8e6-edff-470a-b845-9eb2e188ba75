# BPO Platform

A modern web application for managing Broker Price Opinion (BPO) services, built with React and TypeScript. This platform enables efficient management of customer orders, property valuations, and pricing structures for BPO services.

## Project Overview

The BPO Platform is a comprehensive solution for managing broker price opinion services, featuring:

- Customer management with detailed profiles and billing information
- Order processing with property validation and valuation
- Dynamic pricing structure management
- Role-based access control (Admin/CSR)
- Real-time address validation and property valuation
- Batch upload capabilities for multiple properties

## Technology Stack

- **Frontend Framework**: React 19.0
- **Language**: TypeScript
- **Build Tool**: Vite 6.2
- **State Management**:
  - Jotai for global state
  - React Query for server state
- **UI Components**:
  - Radix UI for accessible components
  - Tailwind CSS for styling
  - Lucide React for icons
- **Form Management**:
  - React Hook Form
  - Zod for validation
- **Authentication**: AWS Amplify/Cognito
- **API Communication**: Axios
- **Development Tools**:
  - ESLint for code quality
  - TypeScript ESLint for type checking

## Prerequisites

- Node.js (v18.0 or higher recommended)
- Bun package manager
- AWS account with Cognito setup (for authentication)

## Installation

1. Clone the repository:

   ```bash
   git clone [repository-url]
   cd fm-BPO
   ```
2. Install dependencies:

   ```bash
   bun install
   ```
3. Set up environment variables:
   Create a `.env` file in the root directory with the following variables:

   ```env
   VITE_AWS_REGION=your_aws_region
   VITE_AWS_USER_POOL_ID=your_user_pool_id
   VITE_AWS_USER_POOL_WEB_CLIENT_ID=your_client_id
   ```

## Development Workflow

1. Start the development server:

   ```bash
   bun run dev
   ```
2. Run linting:

   ```bash
   bun run lint
   ```
3. Build for production:

   ```bash
   bun run build
   ```
4. Preview production build:

   ```bash
   bun run preview
   ```

## Project Structure

```
fm-BPO/
├── src/
│   ├── components/           # React components
│   │   ├── Customer/        # Customer management
│   │   ├── Layout/         # App layout components
│   │   ├── Order/          # Order processing
│   │   ├── ProductPricing/ # Pricing management
│   │   └── ui/            # Reusable UI components
│   ├── jotai/              # Global state management
│   ├── services/           # API services
│   ├── utils/              # Utility functions
│   └── lib/               # Shared libraries
├── public/                 # Static assets
└── vite.config.ts         # Vite configuration
```

## Key Features

### Customer Management

- Create and manage customer profiles
- Handle billing information and addresses
- Manage special pricing arrangements

### Order Processing

- Multi-step order creation
- Real-time address validation
- Property valuation with confidence scoring
- Batch upload capabilities for multiple properties

### Product Pricing

- Standard and special pricing management
- Role-based pricing controls
- Bulk pricing updates

### User Management

- Role-based access control (Admin/CSR)
- Secure authentication via AWS Cognito
- Profile management

## Build and Deployment

1. Ensure all environment variables are properly set
2. Build the project:
   ```bash
   bun run build
   ```
3. The built files will be in the `dist` directory
4. Deploy the contents of the `dist` directory to your hosting service

## Contributing Guidelines

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

### Code Style

- Follow TypeScript best practices
- Use functional components with hooks
- Maintain component modularity
- Write meaningful commit messages
- Add appropriate comments and documentation

## License

This project is proprietary software. All rights reserved.

## Support

For support, please contact the development team or raise an issue in the repository.
