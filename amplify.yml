version: 1
env:
  variables:
    SERVER_TYPE_PROD: prod
    SERVER_TYPE_EXP: exp
    AWS_COGNITO_REGION: us-east-1
    AWS_COGNITO_IDENTITY_POOL: us-east-1:842ab1b1-3488-4d69-bf11-57a05a80aaf8
frontend:
  phases:
    preBuild:
      commands:
        # - if [ "${AWS_BRANCH}" = "main" ]; then export UMI_APP_SERVER_TYPE=${SERVER_TYPE_PROD}; fi
        # - if [ "${AWS_BRANCH}" = "RELEASE-CANDIDATE-2" ]; then export UMI_APP_SERVER_TYPE=${SERVER_TYPE_EXP}; fi
        # - export UMI_APP_AWS_COGNITO_REGION=${AWS_COGNITO_REGION}
        # - export UMI_APP_AWS_COGNITO_IDENTITY_POOL=${AWS_COGNITO_IDENTITY_POOL}
        # - if [ "${AWS_BRANCH}" = "main" ]; then echo "UMI_APP_SERVER_TYPE=${SERVER_TYPE_PROD}" > .env; fi
        # - if [ "${AWS_BRANCH}" = "staging" ]; then echo "UMI_APP_SERVER_TYPE=${SERVER_TYPE_PROD}" > .env; fi
        # - if [ "${AWS_BRANCH}" = "RELEASE-CANDIDATE-2" ]; then echo "UMI_APP_SERVER_TYPE=${SERVER_TYPE_EXP}" > .env; fi
        # - echo "UMI_APP_AWS_COGNITO_IDENTITY_POOL=${AWS_COGNITO_IDENTITY_POOL}" >> .env
        # - echo "UMI_APP_AWS_COGNITO_REGION=${AWS_COGNITO_REGION}" >> .env
        - curl -fsSL https://bun.sh/install | bash
        - source /root/.bashrc
        - bun install
        # - if [ "${AWS_BRANCH}" = "RELEASE-CANDIDATE-2" ]; then bun update "@spatiallaser/map"; fi
        # - bash bootstrap-build-env.sh
    build:
      commands:
        - bun run build
  artifacts:
    # IMPORTANT - Please verify your build output directory
    baseDirectory: dist
    files:
      - '**/*'
  # cache:
  #   paths:
  #     - node_modules/**/*
