import path from "path";
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import tailwindcss from "@tailwindcss/vite";
// https://vite.dev/config/
export default defineConfig({
  plugins: [react(), tailwindcss()],

  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  server: {
    proxy: {
      "/api/underwriting/exp": {
        target: "http://ec2-3-235-170-15.compute-1.amazonaws.com:8046",
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api\/underwriting\/exp/, ""), // Remove entire prefix
      },
      "/api/underwriting/prod": {
        target: "http://ec2-54-146-231-140.compute-1.amazonaws.com:8046",
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api\/underwriting\/prod/, ""), // Remove entire prefix
      },
      "/api/admin-server/exp": {
        target: "http://ec2-3-235-170-15.compute-1.amazonaws.com:9027",
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api\/admin-server\/exp/, ""), // Remove entire prefix
      },
      "/api/admin-server/prod": {
        target: "http://ec2-54-146-231-140.compute-1.amazonaws.com:9027",
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api\/admin-server\/prod/, ""), // Remove entire prefix
      },
      "/api/bpo/prod": {
        target: "http://ec2-44-222-3-252.compute-1.amazonaws.com:8044",
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api\/bpo\/prod/, ""), // Remove entire prefix
      },
      "/api/cma/exp": {
        target: "http://ec2-44-222-3-252.compute-1.amazonaws.com:8080",
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api\/cma\/exp/, ""), // Remove entire prefix
      },
      "/api/cma/prod": {
        target: "http://ec2-44-222-3-252.compute-1.amazonaws.com:8080",
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api\/cma\/prod/, ""), // Remove entire prefix
      },
    },
  },
});
